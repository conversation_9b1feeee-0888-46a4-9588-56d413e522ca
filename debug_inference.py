"""
调试推理脚本 - 诊断为什么输出是噪声
"""

import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import os

from diffusion_model import DiffusionSRFusion
from diffusion_scheduler import get_scheduler


def load_model_debug(model_path, device):
    """加载模型并显示详细信息"""
    print(f"加载模型: {model_path}")
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return None, None
    
    checkpoint = torch.load(model_path, map_location=device)
    
    # 显示检查点信息
    print(f"检查点信息:")
    print(f"  训练epoch: {checkpoint.get('epoch', 'unknown')}")
    print(f"  训练损失: {checkpoint.get('loss', 'unknown')}")
    print(f"  配置: {checkpoint.get('config', {})}")
    
    # 创建模型
    model = DiffusionSRFusion(
        sar_channels=1,
        ms_channels=4,
        output_channels=3
    ).to(device)
    
    # 加载权重
    try:
        model.load_state_dict(checkpoint['model_state_dict'])
        print("✅ 模型权重加载成功")
    except Exception as e:
        print(f"❌ 模型权重加载失败: {e}")
        return None, None
    
    model.eval()
    return model, checkpoint.get('config', {})


def test_model_output(model, device):
    """测试模型输出是否合理"""
    print("\n=== 测试模型输出 ===")
    
    # 创建测试输入
    batch_size = 1
    noisy_img = torch.randn(batch_size, 3, 64, 64, device=device)
    time = torch.randint(0, 100, (batch_size,), device=device)
    sar = torch.rand(batch_size, 1, 64, 64, device=device)
    ms = torch.rand(batch_size, 4, 64, 64, device=device)
    
    print(f"输入范围:")
    print(f"  噪声图像: [{noisy_img.min():.3f}, {noisy_img.max():.3f}]")
    print(f"  SAR: [{sar.min():.3f}, {sar.max():.3f}]")
    print(f"  多光谱: [{ms.min():.3f}, {ms.max():.3f}]")
    
    with torch.no_grad():
        # 测试不同时间步的输出
        for t in [0, 50, 99]:
            time_tensor = torch.full((batch_size,), t, device=device, dtype=torch.long)
            output = model(noisy_img, time_tensor, sar, ms, apply_sr=False)
            print(f"  时间步 {t}: 输出范围 [{output.min():.3f}, {output.max():.3f}]")
            
            # 检查输出是否全为NaN或无穷大
            if torch.isnan(output).any():
                print(f"    ⚠️ 输出包含NaN值")
            if torch.isinf(output).any():
                print(f"    ⚠️ 输出包含无穷大值")


def simple_denoise_test(model, scheduler, device, num_steps=20):
    """简化的去噪测试 - 使用更稳定的方法"""
    print(f"\n=== 简化去噪测试 ({num_steps}步) ===")

    # 创建测试数据
    batch_size = 1
    sar = torch.rand(batch_size, 1, 64, 64, device=device)
    ms = torch.rand(batch_size, 4, 64, 64, device=device)

    # 从纯噪声开始
    current_sample = torch.randn(batch_size, 3, 64, 64, device=device)
    print(f"初始噪声范围: [{current_sample.min():.3f}, {current_sample.max():.3f}]")

    # 创建时间步序列
    timesteps = torch.linspace(scheduler.num_timesteps - 1, 0, num_steps, dtype=torch.long, device=device)

    with torch.no_grad():
        for i, timestep in enumerate(timesteps):
            timestep_tensor = torch.full((batch_size,), timestep, device=device, dtype=torch.long)

            # 预测噪声
            predicted_noise = model(current_sample, timestep_tensor, sar, ms, apply_sr=False)

            # 使用更简单稳定的去噪方法
            # 直接减去预测的噪声，并添加小的学习率
            learning_rate = 0.1  # 小的学习率防止发散
            current_sample = current_sample - learning_rate * predicted_noise

            # 限制值范围
            current_sample = torch.clamp(current_sample, -2, 2)

            # 打印进度
            if i % 5 == 0 or i == len(timesteps) - 1:
                print(f"  步骤 {i+1}/{len(timesteps)}: 范围 [{current_sample.min():.3f}, {current_sample.max():.3f}]")

    return current_sample


def alternative_denoise_test(model, scheduler, device, num_steps=50):
    """替代的去噪测试 - 使用调度器的内置方法"""
    print(f"\n=== 替代去噪测试 ({num_steps}步) ===")

    # 创建测试数据
    batch_size = 1
    sar = torch.rand(batch_size, 1, 64, 64, device=device)
    ms = torch.rand(batch_size, 4, 64, 64, device=device)

    # 从纯噪声开始
    current_sample = torch.randn(batch_size, 3, 64, 64, device=device)
    print(f"初始噪声范围: [{current_sample.min():.3f}, {current_sample.max():.3f}]")

    # 使用更少的时间步
    timesteps = torch.linspace(999, 0, num_steps, dtype=torch.long, device=device)

    with torch.no_grad():
        for i, timestep in enumerate(timesteps):
            timestep_tensor = torch.full((batch_size,), timestep, device=device, dtype=torch.long)

            # 预测噪声
            predicted_noise = model(current_sample, timestep_tensor, sar, ms, apply_sr=False)

            # 使用调度器的step方法
            try:
                current_sample, _ = scheduler.step(predicted_noise, timestep, current_sample)
            except:
                # 如果调度器step失败，使用简单方法
                current_sample = current_sample - 0.02 * predicted_noise
                current_sample = torch.clamp(current_sample, -2, 2)

            # 打印进度
            if i % 10 == 0 or i == len(timesteps) - 1:
                print(f"  步骤 {i+1}/{len(timesteps)}: 范围 [{current_sample.min():.3f}, {current_sample.max():.3f}]")

    return current_sample


def save_debug_image(tensor, path, title=""):
    """保存调试图像"""
    if tensor.dim() == 4:
        tensor = tensor.squeeze(0)
    
    # 标准化到[0, 1]
    min_val = tensor.min()
    max_val = tensor.max()
    if max_val > min_val:
        tensor = (tensor - min_val) / (max_val - min_val)
    
    # 转换为numpy
    img_np = tensor.cpu().numpy()
    if img_np.shape[0] <= 3:
        img_np = np.transpose(img_np, (1, 2, 0))
    
    # 如果是单通道，转换为RGB
    if img_np.shape[-1] == 1:
        img_np = np.repeat(img_np, 3, axis=-1)
    elif img_np.shape[-1] > 3:
        img_np = img_np[:, :, :3]
    
    # 保存图像
    img_np = (img_np * 255).astype(np.uint8)
    Image.fromarray(img_np).save(path)
    print(f"保存调试图像: {path} ({title})")


def main():
    """主调试函数"""
    print("🔍 扩散模型推理调试")
    print("=" * 50)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 模型路径
    model_path = "./data/diffusion_models/best_diffusion_model_20250708_1912.pth"
    
    # 加载模型
    model, config = load_model_debug(model_path, device)
    if model is None:
        print("❌ 无法加载模型，退出调试")
        return
    
    # 测试模型输出
    test_model_output(model, device)
    
    # 创建调度器
    scheduler = get_scheduler('ddpm', num_timesteps=1000, device=device)
    print(f"\n使用调度器: DDPM, 时间步数: {scheduler.num_timesteps}")
    
    # 简化去噪测试
    result1 = simple_denoise_test(model, scheduler, device, num_steps=20)

    # 替代去噪测试
    result2 = alternative_denoise_test(model, scheduler, device, num_steps=50)

    # 选择更好的结果
    if result1.std() < result2.std():
        result = result1
        method = "简化方法"
    else:
        result = result2
        method = "替代方法"

    print(f"\n选择 {method} 的结果")
    
    # 保存结果
    os.makedirs("./debug_output", exist_ok=True)
    save_debug_image(result, "./debug_output/debug_result.png", "去噪结果")
    
    # 对比：保存纯噪声图像
    noise_img = torch.randn(1, 3, 64, 64)
    save_debug_image(noise_img, "./debug_output/pure_noise.png", "纯噪声")
    
    # 对比：保存随机图像
    random_img = torch.rand(1, 3, 64, 64)
    save_debug_image(random_img, "./debug_output/random_image.png", "随机图像")
    
    print(f"\n📊 结果分析:")
    print(f"  最终结果范围: [{result.min():.3f}, {result.max():.3f}]")
    print(f"  结果标准差: {result.std():.3f}")
    print(f"  结果均值: {result.mean():.3f}")
    
    # 判断结果质量
    if result.std() > 2.0:
        print("⚠️  结果看起来像噪声（标准差过大）")
    elif torch.isnan(result).any():
        print("❌ 结果包含NaN值")
    elif torch.isinf(result).any():
        print("❌ 结果包含无穷大值")
    else:
        print("✅ 结果看起来正常")
    
    print(f"\n调试图像保存在: ./debug_output/")
    print("请检查这些图像来判断问题所在。")


if __name__ == '__main__':
    main()
