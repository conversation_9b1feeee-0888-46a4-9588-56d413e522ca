import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
import os
import numpy as np
import argparse
import logging
from datetime import datetime
from tqdm import tqdm
import rasterio
from rasterio.transform import from_bounds

from diffusion_model import DiffusionSRFusion
from diffusion_scheduler import get_scheduler
from diffusion_dataset import DiffusionInferenceDataset


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='扩散模型SAR-多光谱融合推理脚本')
    
    # 数据参数
    parser.add_argument('--MS_data_dir', type=str, default='./data/predit/MS', help='多光谱图像目录')
    parser.add_argument('--SAR_data_dir', type=str, default='./data/predit/SAR', help='SAR图像目录')
    parser.add_argument('--output_dir', type=str,  default="./data/out", help='输出目录')
    parser.add_argument('--model_path', type=str, default="./data/diffusion_models/best_diffusion_model_20250708_1912.pth", help='模型路径')
    
    # 推理参数
    parser.add_argument('--image_size', type=int, default=256, help='推理图像尺寸')
    parser.add_argument('--batch_size', type=int, default=1, help='推理批次大小')
    parser.add_argument('--num_inference_steps', type=int, default=50, help='推理步数')
    parser.add_argument('--guidance_scale', type=float, default=1.0, help='引导尺度')
    
    # 扩散参数
    parser.add_argument('--scheduler_type', type=str, default='ddim', 
                       choices=['ddpm', 'ddim', 'cosine'], help='推理调度器类型')
    parser.add_argument('--num_timesteps', type=int, default=1000, help='扩散时间步数')
    
    # 其他参数
    parser.add_argument('--cuda', action='store_true', default=True, help='使用CUDA')
    parser.add_argument('--save_intermediate', action='store_true', help='保存中间结果')
    parser.add_argument('--apply_sr', action='store_true', default=True, help='应用超分辨率')
    
    return parser.parse_args()


def load_model(model_path, device):
    """加载训练好的模型"""
    checkpoint = torch.load(model_path, map_location=device)
    
    # 创建模型
    model = DiffusionSRFusion(
        sar_channels=1,
        ms_channels=4,
        output_channels=3
    ).to(device)
    
    # 加载权重
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # 获取配置
    config = checkpoint.get('config', {})
    
    logging.info(f"模型加载成功，训练epoch: {checkpoint.get('epoch', 'unknown')}")
    logging.info(f"模型损失: {checkpoint.get('loss', 'unknown')}")
    
    return model, config


def denoise_image(model, scheduler, noisy_image, sar, ms, num_inference_steps, device, guidance_scale=1.0):
    """使用扩散模型去噪生成图像"""
    model.eval()

    # 设置推理时间步 - 修复：确保正确的时间步序列
    if hasattr(scheduler, 'timesteps') and len(scheduler.timesteps) > 0:
        # 对于DDIM调度器
        timesteps = scheduler.timesteps[:num_inference_steps]
    else:
        # 对于DDPM调度器，创建均匀分布的时间步
        timesteps = torch.linspace(scheduler.num_timesteps - 1, 0, num_inference_steps, dtype=torch.long, device=device)

    current_sample = noisy_image.clone()

    print(f"开始去噪，时间步范围: {timesteps[0]} -> {timesteps[-1]}")

    with torch.no_grad():
        for i, timestep in enumerate(tqdm(timesteps, desc="去噪进度")):
            # 准备时间步张量
            timestep_tensor = torch.full((current_sample.shape[0],), timestep, device=device, dtype=torch.long)

            # 预测噪声
            predicted_noise = model(current_sample, timestep_tensor, sar, ms, apply_sr=False)

            # 应用引导（如果需要）
            if guidance_scale != 1.0:
                # 无条件预测
                uncond_noise = model(current_sample, timestep_tensor,
                                   torch.zeros_like(sar), torch.zeros_like(ms), apply_sr=False)
                # 应用分类器自由引导
                predicted_noise = uncond_noise + guidance_scale * (predicted_noise - uncond_noise)

            # 执行去噪步骤 - 修复：使用正确的DDPM去噪公式
            if hasattr(scheduler, 'step') and hasattr(scheduler, 'timesteps'):
                # 使用调度器的内置step方法
                current_sample, _ = scheduler.step(predicted_noise, timestep, current_sample)
            else:
                # 手动实现DDPM去噪步骤
                alpha_t = scheduler.alphas_cumprod[timestep]
                sqrt_alpha_t = torch.sqrt(scheduler.alphas[timestep])
                sqrt_one_minus_alpha_cumprod_t = scheduler.sqrt_one_minus_alphas_cumprod[timestep]

                # 预测原始图像 x_0
                pred_original = (current_sample - sqrt_one_minus_alpha_cumprod_t * predicted_noise) / torch.sqrt(alpha_t)

                # 计算前一时间步
                if i < len(timesteps) - 1:  # 不是最后一步
                    next_timestep = timesteps[i + 1]
                    alpha_t_prev = scheduler.alphas_cumprod[next_timestep]

                    # 计算前一时间步的均值
                    pred_sample_direction = torch.sqrt(1 - alpha_t_prev) * predicted_noise
                    current_sample = torch.sqrt(alpha_t_prev) * pred_original + pred_sample_direction

                    # 添加噪声（除了最后一步）
                    if next_timestep > 0:
                        variance = scheduler.posterior_variance[timestep]
                        noise = torch.randn_like(current_sample)
                        current_sample += torch.sqrt(variance) * noise
                else:
                    # 最后一步，直接使用预测的原始图像
                    current_sample = pred_original

            # 打印调试信息
            if i % 10 == 0 or i == len(timesteps) - 1:
                print(f"步骤 {i+1}/{len(timesteps)}, 时间步: {timestep}, 图像范围: [{current_sample.min():.3f}, {current_sample.max():.3f}]")

    return current_sample


def apply_super_resolution(model, image, sar, ms):
    """应用超分辨率"""
    with torch.no_grad():
        # 使用模型的超分辨率组件
        sr_image = model.sr_upsampler(image)
    return sr_image


def save_result(image_tensor, output_path, reference_path=None):
    """保存结果图像"""
    # 转换为numpy数组
    if image_tensor.dim() == 4:
        image_tensor = image_tensor.squeeze(0)

    # 标准化到[0, 1]范围
    min_val = image_tensor.min()
    max_val = image_tensor.max()
    if max_val > min_val:
        image_tensor = (image_tensor - min_val) / (max_val - min_val)
    else:
        image_tensor = torch.clamp(image_tensor, 0, 1)

    print(f"保存图像范围: [{image_tensor.min():.3f}, {image_tensor.max():.3f}]")

    # 转换为numpy并调整维度顺序
    image_np = image_tensor.cpu().numpy()
    if image_np.shape[0] <= 3:  # (C, H, W) -> (H, W, C)
        image_np = np.transpose(image_np, (1, 2, 0))

    # 转换为uint8格式 (0-255)
    image_np = (image_np * 255).astype(np.uint8)
    
    # 获取参考图像的地理信息（如果提供）
    if reference_path and os.path.exists(reference_path):
        try:
            with rasterio.open(reference_path) as src:
                profile = src.profile.copy()
                profile.update({
                    'dtype': 'uint16',
                    'count': image_np.shape[-1] if image_np.ndim == 3 else 1,
                    'height': image_np.shape[0],
                    'width': image_np.shape[1]
                })
        except:
            profile = {
                'driver': 'GTiff',
                'dtype': 'uint16',
                'count': image_np.shape[-1] if image_np.ndim == 3 else 1,
                'height': image_np.shape[0],
                'width': image_np.shape[1],
                'crs': 'EPSG:4326'
            }
    else:
        profile = {
            'driver': 'GTiff',
            'dtype': 'uint16',
            'count': image_np.shape[-1] if image_np.ndim == 3 else 1,
            'height': image_np.shape[0],
            'width': image_np.shape[1],
            'crs': 'EPSG:4326'
        }
    
    # 保存图像
    with rasterio.open(output_path, 'w', **profile) as dst:
        if image_np.ndim == 3:
            for i in range(image_np.shape[-1]):
                dst.write(image_np[:, :, i], i + 1)
        else:
            dst.write(image_np, 1)


def main():
    """主推理函数"""
    args = parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 确定设备
    device = torch.device('cuda' if torch.cuda.is_available() and args.cuda else 'cpu')
    logging.info(f"使用设备: {device}")
    
    # 加载模型
    model, config = load_model(args.model_path, device)
    
    # 创建噪声调度器
    scheduler = get_scheduler(
        scheduler_type=args.scheduler_type,
        num_timesteps=args.num_timesteps,
        num_inference_steps=args.num_inference_steps,
        device=device
    )
    
    # 创建数据集
    dataset = DiffusionInferenceDataset(
        MS_data_dir=args.MS_data_dir,
        SAR_data_dir=args.SAR_data_dir,
        image_size=args.image_size,
        normalize=True
    )
    
    # 创建数据加载器
    dataloader = DataLoader(
        dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=0  # 推理时使用单进程
    )
    
    logging.info(f"开始推理，共 {len(dataset)} 个样本")
    
    # 开始推理
    for batch_idx, batch in enumerate(tqdm(dataloader, desc="推理进度")):
        ms = batch['ms'].to(device)
        sar = batch['sar'].to(device)
        filenames = batch['filename']
        
        batch_size = ms.shape[0]
        
        # 生成初始噪声
        noise_shape = (batch_size, 3, args.image_size, args.image_size)
        initial_noise = torch.randn(noise_shape, device=device)
        
        # 扩散去噪生成图像
        generated_image = denoise_image(
            model, scheduler, initial_noise, sar, ms,
            args.num_inference_steps, device, args.guidance_scale
        )
        
        # 应用超分辨率（如果启用）
        if args.apply_sr:
            generated_image = apply_super_resolution(model, generated_image, sar, ms)
        
        # 保存结果
        for i in range(batch_size):
            filename = filenames[i]
            base_name = os.path.splitext(filename)[0]
            
            # 保存融合结果
            output_path = os.path.join(args.output_dir, f"{base_name}_fused.tif")
            reference_path = os.path.join(args.MS_data_dir, filename)
            save_result(generated_image[i], output_path, reference_path)
            
            # 保存中间结果（如果启用）
            if args.save_intermediate:
                # 保存原始多光谱图像
                ms_output_path = os.path.join(args.output_dir, f"{base_name}_ms.tif")
                save_result(ms[i], ms_output_path, reference_path)
                
                # 保存SAR图像
                sar_output_path = os.path.join(args.output_dir, f"{base_name}_sar.tif")
                save_result(sar[i], sar_output_path, reference_path)
            
            logging.info(f"已处理: {filename} -> {output_path}")
    
    logging.info(f"推理完成！结果保存在: {args.output_dir}")


if __name__ == '__main__':
    main()
