import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np


class TimeEmbedding(nn.Module):
    """时间步嵌入层"""
    def __init__(self, dim):
        super().__init__()
        self.dim = dim
        
    def forward(self, time):
        device = time.device
        half_dim = self.dim // 2
        embeddings = math.log(10000) / (half_dim - 1)
        embeddings = torch.exp(torch.arange(half_dim, device=device) * -embeddings)
        embeddings = time[:, None] * embeddings[None, :]
        embeddings = torch.cat((embeddings.sin(), embeddings.cos()), dim=-1)
        return embeddings


class ResBlock(nn.Module):
    """残差块，支持时间和条件嵌入"""
    def __init__(self, in_channels, out_channels, time_emb_dim, cond_emb_dim=None):
        super().__init__()
        self.time_mlp = nn.Linear(time_emb_dim, out_channels)
        
        self.block1 = nn.Sequential(
            nn.GroupNorm(1, in_channels),
            nn.SiLU(),
            nn.Conv2d(in_channels, out_channels, 3, padding=1)
        )
        
        self.block2 = nn.Sequential(
            nn.GroupNorm(1, out_channels),
            nn.SiLU(),
            nn.Conv2d(out_channels, out_channels, 3, padding=1)
        )
        
        # 条件嵌入处理
        if cond_emb_dim:
            self.cond_mlp = nn.Linear(cond_emb_dim, out_channels)
        else:
            self.cond_mlp = None
            
        # 残差连接
        if in_channels != out_channels:
            self.residual_conv = nn.Conv2d(in_channels, out_channels, 1)
        else:
            self.residual_conv = nn.Identity()
    
    def forward(self, x, time_emb, cond_emb=None):
        h = self.block1(x)
        
        # 添加时间嵌入
        time_emb = self.time_mlp(time_emb)
        h = h + time_emb[:, :, None, None]
        
        # 添加条件嵌入
        if self.cond_mlp is not None and cond_emb is not None:
            cond_emb = self.cond_mlp(cond_emb)
            h = h + cond_emb[:, :, None, None]
        
        h = self.block2(h)
        
        return h + self.residual_conv(x)


class AttentionBlock(nn.Module):
    """自注意力块"""
    def __init__(self, channels):
        super().__init__()
        self.channels = channels
        self.group_norm = nn.GroupNorm(1, channels)
        self.q = nn.Conv2d(channels, channels, 1)
        self.k = nn.Conv2d(channels, channels, 1)
        self.v = nn.Conv2d(channels, channels, 1)
        self.proj_out = nn.Conv2d(channels, channels, 1)
    
    def forward(self, x):
        B, C, H, W = x.shape
        h = self.group_norm(x)
        
        q = self.q(h).view(B, C, H * W).transpose(1, 2)
        k = self.k(h).view(B, C, H * W)
        v = self.v(h).view(B, C, H * W).transpose(1, 2)
        
        # 计算注意力权重
        attention = torch.bmm(q, k) * (C ** -0.5)
        attention = F.softmax(attention, dim=-1)
        
        # 应用注意力
        h = torch.bmm(attention, v).transpose(1, 2).view(B, C, H, W)
        h = self.proj_out(h)
        
        return x + h


class MultiModalEncoder(nn.Module):
    """多模态编码器，处理SAR和多光谱输入"""
    def __init__(self, sar_channels=1, ms_channels=4, embed_dim=256):
        super().__init__()
        
        # SAR编码器
        self.sar_encoder = nn.Sequential(
            nn.Conv2d(sar_channels, 64, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 128, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(128, embed_dim // 2)
        )
        
        # 多光谱编码器
        self.ms_encoder = nn.Sequential(
            nn.Conv2d(ms_channels, 64, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 128, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(128, embed_dim // 2)
        )
        
        # 融合层
        self.fusion = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(inplace=True),
            nn.Linear(embed_dim, embed_dim)
        )
    
    def forward(self, sar, ms):
        sar_feat = self.sar_encoder(sar)
        ms_feat = self.ms_encoder(ms)
        
        # 特征融合
        combined = torch.cat([sar_feat, ms_feat], dim=1)
        fused = self.fusion(combined)
        
        return fused


class ConditionalUNet(nn.Module):
    """条件UNet，用于扩散模型"""
    def __init__(self, in_channels=3, out_channels=3, time_emb_dim=256, 
                 cond_emb_dim=256, base_channels=64):
        super().__init__()
        
        # 时间嵌入
        self.time_embedding = TimeEmbedding(time_emb_dim)
        
        # 编码器
        self.down1 = ResBlock(in_channels, base_channels, time_emb_dim, cond_emb_dim)
        self.down2 = ResBlock(base_channels, base_channels * 2, time_emb_dim, cond_emb_dim)
        self.down3 = ResBlock(base_channels * 2, base_channels * 4, time_emb_dim, cond_emb_dim)
        
        # 中间层
        self.mid1 = ResBlock(base_channels * 4, base_channels * 4, time_emb_dim, cond_emb_dim)
        self.mid_attn = AttentionBlock(base_channels * 4)
        self.mid2 = ResBlock(base_channels * 4, base_channels * 4, time_emb_dim, cond_emb_dim)
        
        # 解码器
        self.up1 = ResBlock(base_channels * 8, base_channels * 2, time_emb_dim, cond_emb_dim)
        self.up2 = ResBlock(base_channels * 4, base_channels, time_emb_dim, cond_emb_dim)
        self.up3 = ResBlock(base_channels * 2, base_channels, time_emb_dim, cond_emb_dim)
        
        # 输出层
        self.out = nn.Sequential(
            nn.GroupNorm(8, base_channels),
            nn.SiLU(),
            nn.Conv2d(base_channels, out_channels, 3, padding=1)
        )
        
        # 下采样和上采样
        self.downsample = nn.MaxPool2d(2)
        self.upsample = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=False)
    
    def forward(self, x, time, cond_emb):
        # 时间嵌入
        time_emb = self.time_embedding(time)
        
        # 编码器
        h1 = self.down1(x, time_emb, cond_emb)
        h2 = self.down2(self.downsample(h1), time_emb, cond_emb)
        h3 = self.down3(self.downsample(h2), time_emb, cond_emb)
        
        # 中间层
        h = self.mid1(self.downsample(h3), time_emb, cond_emb)
        h = self.mid_attn(h)
        h = self.mid2(h, time_emb, cond_emb)
        
        # 解码器（带跳跃连接）
        h = self.up1(torch.cat([self.upsample(h), h3], dim=1), time_emb, cond_emb)
        h = self.up2(torch.cat([self.upsample(h), h2], dim=1), time_emb, cond_emb)
        h = self.up3(torch.cat([self.upsample(h), h1], dim=1), time_emb, cond_emb)
        
        return self.out(h)


class DiffusionSRFusion(nn.Module):
    """扩散超分辨融合模型"""
    def __init__(self, sar_channels=1, ms_channels=4, output_channels=3,
                 time_emb_dim=256, cond_emb_dim=256):
        super().__init__()
        
        # 多模态编码器
        self.modal_encoder = MultiModalEncoder(sar_channels, ms_channels, cond_emb_dim)
        
        # 条件UNet
        self.unet = ConditionalUNet(
            in_channels=output_channels,
            out_channels=output_channels,
            time_emb_dim=time_emb_dim,
            cond_emb_dim=cond_emb_dim
        )
        
        # 超分辨率上采样（可选）
        self.sr_upsampler = nn.Sequential(
            nn.Conv2d(output_channels, output_channels * 4, 3, padding=1),
            nn.PixelShuffle(2),
            nn.ReLU(inplace=True),
            nn.Conv2d(output_channels, output_channels, 3, padding=1)
        )
    
    def forward(self, noisy_img, time, sar, ms, apply_sr=True):
        # 编码条件信息
        cond_emb = self.modal_encoder(sar, ms)
        
        # UNet去噪
        denoised = self.unet(noisy_img, time, cond_emb)
        
        # 可选的超分辨率上采样
        if apply_sr:
            denoised = self.sr_upsampler(denoised)
        
        return denoised
