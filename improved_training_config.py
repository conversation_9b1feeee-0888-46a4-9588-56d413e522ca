"""
改进的训练配置 - 获得更好的融合效果
"""

import os
import argparse


def get_improved_training_args():
    """获取改进的训练参数配置"""
    
    # 基础配置
    base_config = {
        # 数据参数
        'MS_data_dir': './data/train/MS',
        'SAR_data_dir': './data/train/SAR',
        'image_size': 256,
        'scale': 4,
        
        # 训练参数 - 大幅增加
        'epochs': 200,  # 从28增加到200
        'batch_size': 4,  # 减小批次大小以适应更复杂的损失
        'lr': 5e-5,  # 降低学习率以获得更稳定的训练
        'weight_decay': 1e-6,
        'val_ratio': 0.1,
        'seed': 42,
        
        # 扩散模型参数
        'num_timesteps': 1000,
        'scheduler_type': 'cosine',  # 使用余弦调度器
        'beta_start': 0.0001,
        'beta_end': 0.02,
        
        # 损失函数权重 - 启用更多损失
        'lambda_mse': 1.0,
        'lambda_perceptual': 0.1,  # 启用感知损失
        'lambda_structural': 0.1,  # 启用结构损失
        'lambda_edge': 0.05,       # 启用边缘损失
        'lambda_spectral': 0.05,   # 启用光谱损失
        
        # 其他参数
        'save_dir': './data/diffusion_models_improved',
        'save_interval': 20,  # 每20个epoch保存一次
        'cuda': True,
        'num_workers': 2
    }
    
    return base_config


def get_improved_inference_args():
    """获取改进的推理参数配置"""
    
    inference_config = {
        # 数据参数
        'MS_data_dir': './data/predit/MS',
        'SAR_data_dir': './data/predit/SAR',
        'output_dir': './data/out_improved_v2',
        'model_path': './data/diffusion_models_improved/best_diffusion_model.pth',
        
        # 推理参数 - 大幅增加步数
        'image_size': 256,
        'batch_size': 1,
        'num_inference_steps': 200,  # 从50增加到200
        'guidance_scale': 1.5,  # 增加引导强度
        
        # 扩散参数
        'scheduler_type': 'ddim',  # 使用DDIM获得更好的质量
        'num_timesteps': 1000,
        
        # 其他参数
        'apply_sr': True,
        'save_intermediate': True,
        'cuda': True
    }
    
    return inference_config


def create_training_script():
    """创建改进的训练脚本"""
    
    config = get_improved_training_args()
    
    # 生成训练命令
    train_cmd = f"""python diffusion_train.py \\
    --MS_data_dir {config['MS_data_dir']} \\
    --SAR_data_dir {config['SAR_data_dir']} \\
    --epochs {config['epochs']} \\
    --batch_size {config['batch_size']} \\
    --lr {config['lr']} \\
    --weight_decay {config['weight_decay']} \\
    --scheduler_type {config['scheduler_type']} \\
    --num_timesteps {config['num_timesteps']} \\
    --lambda_mse {config['lambda_mse']} \\
    --lambda_perceptual {config['lambda_perceptual']} \\
    --lambda_structural {config['lambda_structural']} \\
    --lambda_edge {config['lambda_edge']} \\
    --lambda_spectral {config['lambda_spectral']} \\
    --save_dir {config['save_dir']} \\
    --save_interval {config['save_interval']} \\
    --num_workers {config['num_workers']}"""
    
    return train_cmd


def create_inference_script():
    """创建改进的推理脚本"""
    
    config = get_improved_inference_args()
    
    # 生成推理命令
    inference_cmd = f"""python diffusion_inference.py \\
    --MS_data_dir {config['MS_data_dir']} \\
    --SAR_data_dir {config['SAR_data_dir']} \\
    --output_dir {config['output_dir']} \\
    --model_path {config['model_path']} \\
    --num_inference_steps {config['num_inference_steps']} \\
    --guidance_scale {config['guidance_scale']} \\
    --scheduler_type {config['scheduler_type']} \\
    --apply_sr \\
    --save_intermediate"""
    
    return inference_cmd


def print_training_plan():
    """打印训练计划"""
    
    print("🎯 改进的训练计划")
    print("=" * 50)
    
    print("\n📈 训练改进点:")
    print("1. 训练轮数: 28 → 200 epoch")
    print("2. 学习率: 1e-4 → 5e-5 (更稳定)")
    print("3. 调度器: DDPM → Cosine (更平滑)")
    print("4. 损失函数: 只有MSE → 多种损失组合")
    print("   - MSE损失: 1.0")
    print("   - 感知损失: 0.1 (提升视觉质量)")
    print("   - 结构损失: 0.1 (保持结构)")
    print("   - 边缘损失: 0.05 (保持边缘)")
    print("   - 光谱损失: 0.05 (保持光谱)")
    
    print("\n🔍 推理改进点:")
    print("1. 推理步数: 50 → 200 步")
    print("2. 引导尺度: 1.0 → 1.5")
    print("3. 调度器: DDIM (确定性，高质量)")
    print("4. 保存中间结果用于调试")
    
    print("\n⏱️ 预估时间:")
    print("- 训练时间: ~8-12小时 (200 epoch)")
    print("- 推理时间: ~2-3分钟/图像 (200步)")
    
    print("\n💾 存储需求:")
    print("- 模型检查点: ~2GB (每20个epoch)")
    print("- 最终模型: ~500MB")
    print("- 训练日志: ~100MB")


def create_progressive_training_plan():
    """创建渐进式训练计划"""
    
    plans = [
        {
            'name': '阶段1: 基础训练',
            'epochs': 50,
            'lr': 1e-4,
            'losses': {'mse': 1.0, 'perceptual': 0.0, 'structural': 0.0},
            'description': '只使用MSE损失，建立基础'
        },
        {
            'name': '阶段2: 添加感知损失',
            'epochs': 100,
            'lr': 5e-5,
            'losses': {'mse': 1.0, 'perceptual': 0.05, 'structural': 0.0},
            'description': '逐步添加感知损失'
        },
        {
            'name': '阶段3: 完整损失',
            'epochs': 200,
            'lr': 2e-5,
            'losses': {'mse': 1.0, 'perceptual': 0.1, 'structural': 0.1, 'edge': 0.05, 'spectral': 0.05},
            'description': '使用完整的损失函数组合'
        }
    ]
    
    print("\n🎯 渐进式训练计划 (推荐)")
    print("=" * 50)
    
    for i, plan in enumerate(plans, 1):
        print(f"\n{plan['name']} ({plan['epochs']} epochs)")
        print(f"学习率: {plan['lr']}")
        print(f"损失权重: {plan['losses']}")
        print(f"说明: {plan['description']}")
        
        if i < len(plans):
            print("↓")
    
    return plans


def main():
    """主函数"""
    print("🚀 扩散模型训练和推理改进方案")
    print("=" * 60)
    
    # 打印训练计划
    print_training_plan()
    
    # 渐进式训练计划
    create_progressive_training_plan()
    
    # 生成脚本
    print("\n📝 生成的训练命令:")
    print("-" * 30)
    train_cmd = create_training_script()
    print(train_cmd)
    
    print("\n📝 生成的推理命令:")
    print("-" * 30)
    inference_cmd = create_inference_script()
    print(inference_cmd)
    
    print("\n💡 建议的执行顺序:")
    print("1. 先运行改进的训练 (200 epochs)")
    print("2. 监控训练损失和验证损失")
    print("3. 使用最佳模型进行高质量推理")
    print("4. 如果效果仍不理想，考虑:")
    print("   - 进一步增加训练轮数到300-500")
    print("   - 调整损失函数权重")
    print("   - 使用更大的推理步数(300-500)")
    
    print("\n⚠️ 注意事项:")
    print("- 训练时间会显著增加")
    print("- 需要监控GPU内存使用")
    print("- 建议定期保存检查点")
    print("- 可以从现有模型继续训练")


if __name__ == '__main__':
    main()
