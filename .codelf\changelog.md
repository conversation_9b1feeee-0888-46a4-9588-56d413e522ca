# 项目变更日志

## [2025-01-08] 扩散模型SAR-多光谱融合方案

### 新增功能 ✨

#### 扩散模型核心架构
- **diffusion_model.py**: 实现了完整的扩散模型架构
  - `DiffusionSRFusion`: 主要的扩散超分辨融合模型
  - `ConditionalUNet`: 条件UNet网络，支持时间和条件嵌入
  - `MultiModalEncoder`: 多模态编码器，融合SAR和多光谱特征
  - `TimeEmbedding`: 时间步嵌入层
  - `ResBlock`: 支持条件的残差块
  - `AttentionBlock`: 自注意力机制

#### 噪声调度系统
- **diffusion_scheduler.py**: 实现了多种噪声调度策略
  - `DDPMScheduler`: 标准去噪扩散概率模型调度器
  - `DDIMScheduler`: 确定性推理调度器，支持快速采样
  - `CosineScheduler`: 余弦噪声调度器
  - `get_scheduler()`: 统一的调度器获取接口

#### 综合损失函数
- **diffusion_loss.py**: 设计了多层次损失函数
  - `DiffusionLoss`: 综合扩散损失，整合多种损失组件
  - `PerceptualLoss`: 基于VGG特征的感知损失
  - `StructuralLoss`: 基于SSIM的结构损失
  - `EdgeLoss`: 边缘保持损失
  - `SpectralLoss`: 光谱保真损失
  - `ConsistencyLoss`: 一致性损失
  - `CycleLoss`: 循环一致性损失

#### 数据处理系统
- **diffusion_dataset.py**: 专为扩散模型设计的数据集
  - `DiffusionSARMSDataset`: 训练数据集，支持数据增强和噪声添加
  - `DiffusionInferenceDataset`: 推理数据集
  - `collate_fn`: 自定义批处理函数
  - 支持多种数据增强策略
  - 自动创建融合目标图像

#### 训练系统
- **diffusion_train.py**: 完整的扩散模型训练脚本
  - 支持多种超参数配置
  - 集成验证和模型保存
  - 训练曲线可视化
  - 梯度裁剪和学习率调度
  - 详细的日志记录

#### 推理系统
- **diffusion_inference.py**: 高效的推理脚本
  - 支持批量推理
  - 可配置的推理步数
  - 引导尺度控制
  - 超分辨率集成
  - 地理信息保持

#### 使用示例和文档
- **diffusion_example.py**: 完整的使用示例和演示
  - 模型架构演示
  - 调度器功能展示
  - 损失函数说明
  - 训练和推理流程演示
  - 可视化功能

- **README_DIFFUSION.md**: 详细的使用文档
  - 技术原理说明
  - 安装和配置指南
  - 使用方法和参数说明
  - 性能优化建议
  - 故障排除指南

### 技术特性 🚀

#### 无监督学习
- 不需要高分辨率标签数据
- 自监督重建训练策略
- 循环一致性约束

#### 高质量生成
- 扩散过程生成丰富细节
- 多尺度特征融合
- 注意力机制增强

#### 多模态融合
- SAR结构信息提取
- 多光谱光谱特性保持
- 自适应权重分配

#### 灵活配置
- 多种噪声调度策略
- 可调节的推理步数
- 引导尺度控制

### 性能优势 📈

相比原有SRCNN方法：

1. **数据需求**：无需配对高分辨率标签
2. **细节质量**：显著提升细节生成能力
3. **结构保持**：更好地保持SAR结构信息
4. **光谱保真**：维持多光谱光谱特性
5. **可控性**：支持生成过程控制

### 使用方法 💡

#### 训练命令
```bash
python diffusion_train.py \
    --MS_data_dir ./data/train/MS \
    --SAR_data_dir ./data/train/SAR \
    --epochs 100 \
    --batch_size 8 \
    --lr 1e-4
```

#### 推理命令
```bash
python diffusion_inference.py \
    --MS_data_dir ./data/test/MS \
    --SAR_data_dir ./data/test/SAR \
    --output_dir ./results \
    --model_path ./data/diffusion_models/best_model.pth
```

#### 演示运行
```bash
python diffusion_example.py
```

### 文件结构变更 📁

新增文件：
- `diffusion_model.py` - 扩散模型架构
- `diffusion_scheduler.py` - 噪声调度器
- `diffusion_loss.py` - 损失函数
- `diffusion_dataset.py` - 数据集处理
- `diffusion_train.py` - 训练脚本
- `diffusion_inference.py` - 推理脚本
- `diffusion_example.py` - 使用示例
- `README_DIFFUSION.md` - 扩散模型文档
- `.codelf/project.md` - 项目文档
- `.codelf/changelog.md` - 变更日志

### 兼容性 🔄

- 保持原有SRCNN代码完整性
- 可与现有训练流程并行使用
- 支持结果对比和评估

### 依赖更新 📦

新增依赖：
- 无新增外部依赖
- 充分利用现有PyTorch生态

### 注意事项 ⚠️

1. **内存需求**：扩散模型训练需要更多GPU内存
2. **训练时间**：相比SRCNN需要更长训练时间
3. **推理速度**：推理时间取决于采样步数
4. **参数调优**：需要根据数据特点调整超参数

### 后续计划 🎯

1. 性能优化和加速
2. 模型压缩和量化
3. 实时处理能力
4. 多时相数据支持
5. 移动端部署适配
