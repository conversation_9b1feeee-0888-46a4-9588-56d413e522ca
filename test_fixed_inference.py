"""
测试修复后的推理算法
"""

import torch
import os
from diffusion_model import DiffusionSRFusion
from diffusion_scheduler import get_scheduler
from diffusion_inference import denoise_image, ddpm_step, ddim_step


def load_model_for_test(model_path, device):
    """加载模型用于测试"""
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return None
    
    checkpoint = torch.load(model_path, map_location=device)
    
    model = DiffusionSRFusion(
        sar_channels=1,
        ms_channels=4,
        output_channels=3
    ).to(device)
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    print(f"✅ 模型加载成功，训练epoch: {checkpoint.get('epoch', 'unknown')}")
    return model


def test_ddpm_step():
    """测试DDPM步骤函数"""
    print("\n=== 测试DDPM步骤函数 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    scheduler = get_scheduler('ddpm', num_timesteps=1000, device=device)
    
    # 创建测试数据
    sample = torch.randn(1, 3, 64, 64, device=device)
    predicted_noise = torch.randn(1, 3, 64, 64, device=device)
    timestep = torch.tensor(500, device=device)
    timesteps = torch.linspace(999, 0, 50, dtype=torch.long, device=device)
    step_index = 25  # 中间步骤
    
    print(f"输入样本范围: [{sample.min():.3f}, {sample.max():.3f}]")
    
    try:
        result = ddpm_step(scheduler, predicted_noise, timestep, sample, step_index, timesteps)
        print(f"DDPM步骤结果范围: [{result.min():.3f}, {result.max():.3f}]")
        print("✅ DDPM步骤函数正常")
        return True
    except Exception as e:
        print(f"❌ DDPM步骤函数失败: {e}")
        return False


def test_ddim_step():
    """测试DDIM步骤函数"""
    print("\n=== 测试DDIM步骤函数 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    scheduler = get_scheduler('ddim', num_timesteps=1000, num_inference_steps=50, device=device)
    
    # 创建测试数据
    sample = torch.randn(1, 3, 64, 64, device=device)
    predicted_noise = torch.randn(1, 3, 64, 64, device=device)
    timestep = torch.tensor(500, device=device)
    timesteps = scheduler.timesteps[:50]
    step_index = 25  # 中间步骤
    
    print(f"输入样本范围: [{sample.min():.3f}, {sample.max():.3f}]")
    
    try:
        result = ddim_step(scheduler, predicted_noise, timestep, sample, step_index, timesteps)
        print(f"DDIM步骤结果范围: [{result.min():.3f}, {result.max():.3f}]")
        print("✅ DDIM步骤函数正常")
        return True
    except Exception as e:
        print(f"❌ DDIM步骤函数失败: {e}")
        return False


def test_complete_denoise():
    """测试完整的去噪过程"""
    print("\n=== 测试完整去噪过程 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model_path = "./data/diffusion_models/best_diffusion_model_20250708_1912.pth"
    
    # 加载模型
    model = load_model_for_test(model_path, device)
    if model is None:
        return False
    
    # 测试DDPM调度器
    print("\n--- 测试DDPM调度器 ---")
    scheduler_ddpm = get_scheduler('ddpm', num_timesteps=1000, device=device)
    
    # 创建测试数据
    sar = torch.rand(1, 1, 64, 64, device=device)
    ms = torch.rand(1, 4, 64, 64, device=device)
    initial_noise = torch.randn(1, 3, 64, 64, device=device)
    
    print(f"初始噪声: [{initial_noise.min():.3f}, {initial_noise.max():.3f}], std: {initial_noise.std():.3f}")
    
    try:
        result_ddpm = denoise_image(model, scheduler_ddpm, initial_noise, sar, ms, 20, device)
        print(f"DDPM结果: [{result_ddpm.min():.3f}, {result_ddpm.max():.3f}], std: {result_ddpm.std():.3f}")

        # 检查结果质量
        if result_ddpm.std() < 3.0 and not torch.isnan(result_ddpm).any():
            print("✅ DDPM去噪成功")
            ddpm_success = True
        else:
            print("⚠️ DDPM去噪结果可能有问题")
            ddpm_success = False
    except Exception as e:
        print(f"❌ DDPM去噪失败: {e}")
        ddpm_success = False
    
    # 测试DDIM调度器
    print("\n--- 测试DDIM调度器 ---")
    scheduler_ddim = get_scheduler('ddim', num_timesteps=1000, num_inference_steps=20, device=device)
    
    try:
        result_ddim = denoise_image(model, scheduler_ddim, initial_noise.clone(), sar, ms, 20, device)
        print(f"DDIM结果: [{result_ddim.min():.3f}, {result_ddim.max():.3f}], std: {result_ddim.std():.3f}")
        
        # 检查结果质量
        if result_ddim.std() < 3.0 and not torch.isnan(result_ddim).any():
            print("✅ DDIM去噪成功")
            ddim_success = True
        else:
            print("⚠️ DDIM去噪结果可能有问题")
            ddim_success = False
    except Exception as e:
        print(f"❌ DDIM去噪失败: {e}")
        ddim_success = False
    
    return ddpm_success or ddim_success


def compare_with_old_method():
    """对比新旧去噪方法"""
    print("\n=== 对比新旧去噪方法 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建测试数据
    sample = torch.randn(1, 3, 64, 64, device=device)
    predicted_noise = torch.randn(1, 3, 64, 64, device=device)
    
    print(f"原始样本范围: [{sample.min():.3f}, {sample.max():.3f}]")
    
    # 旧方法（简化版）
    old_result = sample - 0.1 * predicted_noise
    old_result = torch.clamp(old_result, -2, 2)
    print(f"旧方法结果: [{old_result.min():.3f}, {old_result.max():.3f}], std: {old_result.std():.3f}")
    
    # 新方法（DDPM）
    scheduler = get_scheduler('ddpm', num_timesteps=1000, device=device)
    timestep = torch.tensor(500, device=device)
    timesteps = torch.linspace(999, 0, 50, dtype=torch.long, device=device)
    step_index = 25
    
    new_result = ddpm_step(scheduler, predicted_noise, timestep, sample, step_index, timesteps)
    print(f"新方法结果: [{new_result.min():.3f}, {new_result.max():.3f}], std: {new_result.std():.3f}")
    
    # 比较
    old_change = torch.abs(old_result - sample).mean()
    new_change = torch.abs(new_result - sample).mean()
    
    print(f"旧方法平均变化: {old_change:.3f}")
    print(f"新方法平均变化: {new_change:.3f}")
    
    if new_change > 0 and new_change < old_change * 10:
        print("✅ 新方法变化合理")
        return True
    else:
        print("⚠️ 新方法变化可能过大")
        return False


def main():
    """主测试函数"""
    print("🔧 测试修复后的扩散推理算法")
    print("=" * 50)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 运行所有测试
    tests = [
        ("DDPM步骤函数", test_ddpm_step),
        ("DDIM步骤函数", test_ddim_step),
        ("完整去噪过程", test_complete_denoise),
        ("新旧方法对比", compare_with_old_method)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print(f"\n{'='*50}")
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n通过率: {passed}/{len(results)} ({passed/len(results)*100:.1f}%)")
    
    if passed >= len(results) * 0.75:
        print("🎉 大部分测试通过，修复成功！")
        print("现在可以使用修复后的推理脚本:")
        print("python diffusion_inference.py --num_inference_steps 50")
    else:
        print("⚠️ 部分测试失败，可能需要进一步调试")


if __name__ == '__main__':
    main()
