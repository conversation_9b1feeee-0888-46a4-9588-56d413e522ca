"""
测试扩散模型是否能正常运行
"""

import torch
import torch.nn as nn
from diffusion_model import DiffusionSRFusion
from diffusion_scheduler import get_scheduler
from diffusion_loss import DiffusionLoss


def test_model_forward():
    """测试模型前向传播"""
    print("=== 测试模型前向传播 ===")
    
    # 创建模型
    model = DiffusionSRFusion(
        sar_channels=1,
        ms_channels=4,  # 修改为4通道
        output_channels=3
    )
    
    # 创建示例输入
    batch_size = 2
    noisy_img = torch.randn(batch_size, 3, 64, 64)  # 使用较小尺寸测试
    time = torch.randint(0, 1000, (batch_size,))
    sar = torch.randn(batch_size, 1, 64, 64)
    ms = torch.randn(batch_size, 4, 64, 64)  # 4通道多光谱
    
    print(f"输入形状:")
    print(f"  噪声图像: {noisy_img.shape}")
    print(f"  时间步: {time.shape}")
    print(f"  SAR图像: {sar.shape}")
    print(f"  多光谱图像: {ms.shape}")
    
    # 前向传播
    try:
        with torch.no_grad():
            output = model(noisy_img, time, sar, ms, apply_sr=False)
        print(f"输出形状: {output.shape}")
        print("✅ 模型前向传播成功！")
        return True
    except Exception as e:
        print(f"❌ 模型前向传播失败: {e}")
        return False


def test_scheduler():
    """测试噪声调度器"""
    print("\n=== 测试噪声调度器 ===")
    
    try:
        # 创建调度器
        scheduler = get_scheduler('ddpm', num_timesteps=100)
        
        # 创建示例图像
        clean_image = torch.randn(2, 3, 64, 64)
        
        # 采样时间步
        timesteps = scheduler.sample_timesteps(2)
        print(f"采样时间步: {timesteps}")
        
        # 添加噪声
        noise = torch.randn_like(clean_image)
        noisy_image = scheduler.add_noise(clean_image, noise, timesteps)
        
        print(f"原始图像范围: [{clean_image.min():.3f}, {clean_image.max():.3f}]")
        print(f"噪声图像范围: [{noisy_image.min():.3f}, {noisy_image.max():.3f}]")
        print("✅ 噪声调度器测试成功！")
        return True
    except Exception as e:
        print(f"❌ 噪声调度器测试失败: {e}")
        return False


def test_loss_function():
    """测试损失函数"""
    print("\n=== 测试损失函数 ===")
    
    try:
        # 创建损失函数（只使用MSE损失）
        criterion = DiffusionLoss(
            lambda_mse=1.0,
            lambda_perceptual=0.0,  # 禁用感知损失
            lambda_structural=0.0,
            lambda_edge=0.0,
            lambda_spectral=0.0
        )
        
        # 创建示例数据
        pred_noise = torch.randn(2, 3, 64, 64)
        true_noise = torch.randn(2, 3, 64, 64)
        
        # 计算损失
        loss, loss_dict = criterion(pred_noise, true_noise)
        
        print(f"总损失: {loss.item():.4f}")
        print("损失组件:")
        for key, value in loss_dict.items():
            print(f"  {key}: {value:.4f}")
        print("✅ 损失函数测试成功！")
        return True
    except Exception as e:
        print(f"❌ 损失函数测试失败: {e}")
        return False


def test_training_step():
    """测试完整的训练步骤"""
    print("\n=== 测试完整训练步骤 ===")
    
    try:
        # 创建模型和组件
        model = DiffusionSRFusion(sar_channels=1, ms_channels=4, output_channels=3)
        scheduler = get_scheduler('ddpm', num_timesteps=100)
        criterion = DiffusionLoss(lambda_mse=1.0, lambda_perceptual=0.0, 
                                 lambda_structural=0.0, lambda_edge=0.0, lambda_spectral=0.0)
        optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)
        
        # 创建示例数据
        batch_size = 2
        ms = torch.rand(batch_size, 4, 64, 64)  # 4通道多光谱
        sar = torch.rand(batch_size, 1, 64, 64)
        clean_image = torch.rand(batch_size, 3, 64, 64)
        
        # 模拟训练步骤
        model.train()
        
        # 随机采样时间步
        timesteps = scheduler.sample_timesteps(batch_size)
        
        # 生成随机噪声
        noise = torch.randn_like(clean_image)
        
        # 添加噪声到干净图像
        noisy_image = scheduler.add_noise(clean_image, noise, timesteps)
        
        # 清除梯度
        optimizer.zero_grad()
        
        # 前向传播：预测噪声
        predicted_noise = model(noisy_image, timesteps, sar, ms, apply_sr=False)
        
        # 计算损失
        loss, loss_dict = criterion(predicted_noise, noise)
        
        # 反向传播
        loss.backward()
        
        # 更新参数
        optimizer.step()
        
        print(f"训练步骤完成，损失: {loss.item():.4f}")
        print("✅ 完整训练步骤测试成功！")
        return True
    except Exception as e:
        print(f"❌ 完整训练步骤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("扩散模型测试开始")
    print("=" * 50)
    
    # 设置随机种子
    torch.manual_seed(42)
    
    # 运行所有测试
    tests = [
        test_model_forward,
        test_scheduler,
        test_loss_function,
        test_training_step
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    # 总结结果
    print("\n" + "=" * 50)
    print("测试结果总结:")
    test_names = [
        "模型前向传播",
        "噪声调度器",
        "损失函数",
        "完整训练步骤"
    ]
    
    for name, result in zip(test_names, results):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
    
    if all(results):
        print("\n🎉 所有测试通过！可以开始训练了。")
        print("\n建议的训练命令:")
        print("python diffusion_train.py --MS_data_dir ./data/train/MS --SAR_data_dir ./data/train/SAR --epochs 10 --batch_size 4")
    else:
        print("\n⚠️  部分测试失败，请检查错误信息。")


if __name__ == '__main__':
    main()
