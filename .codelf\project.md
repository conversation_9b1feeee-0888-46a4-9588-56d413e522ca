# SAR-多光谱影像融合项目

## 项目概述

本项目实现了SAR（合成孔径雷达）与多光谱影像的融合方法，包含两种主要方案：

1. **传统SRCNN方法**：基于超分辨率卷积神经网络的融合
2. **扩散模型方法**：基于去噪扩散概率模型的无监督超分辨融合

## 技术架构

### 传统SRCNN方案
- **模型文件**: `model.py`
- **训练脚本**: `train.py`
- **测试脚本**: `test.py`
- **数据处理**: `utils/dataset.py`
- **损失函数**: `utils/loss.py`

### 扩散模型方案（新增）
- **模型架构**: `diffusion_model.py`
- **噪声调度**: `diffusion_scheduler.py`
- **损失函数**: `diffusion_loss.py`
- **数据处理**: `diffusion_dataset.py`
- **训练脚本**: `diffusion_train.py`
- **推理脚本**: `diffusion_inference.py`
- **使用示例**: `diffusion_example.py`

## 核心特性

### 扩散模型优势
1. **无监督学习**：不需要高分辨率标签数据
2. **更好的细节生成**：利用扩散过程生成高质量细节
3. **结构保持**：更好地保持SAR影像的结构信息
4. **光谱保真**：维持多光谱影像的光谱特性

### 技术创新
- 条件扩散模型架构
- 多模态特征融合
- 综合损失函数设计
- 灵活的噪声调度策略

## 数据结构

```
data/
├── train/
│   ├── MS/          # 多光谱影像训练数据
│   └── SAR/         # SAR影像训练数据
├── test/
│   ├── MS/          # 多光谱影像测试数据
│   └── SAR/         # SAR影像测试数据
├── model/           # 保存的模型文件
├── diffusion_models/# 扩散模型文件
├── out/             # 输出结果
└── predit/          # 预测结果
```

## 主要组件

### 1. 扩散模型核心
- **DiffusionSRFusion**: 主要的扩散融合模型
- **ConditionalUNet**: 条件UNet网络
- **MultiModalEncoder**: 多模态编码器
- **TimeEmbedding**: 时间步嵌入

### 2. 噪声调度器
- **DDPMScheduler**: 标准DDPM调度
- **DDIMScheduler**: 快速DDIM调度
- **CosineScheduler**: 余弦噪声调度

### 3. 损失函数
- **DiffusionLoss**: 综合扩散损失
- **PerceptualLoss**: 感知损失
- **StructuralLoss**: 结构损失
- **EdgeLoss**: 边缘损失
- **SpectralLoss**: 光谱损失

## 使用方法

### 扩散模型训练
```bash
python diffusion_train.py \
    --MS_data_dir ./data/train/MS \
    --SAR_data_dir ./data/train/SAR \
    --epochs 100 \
    --batch_size 8
```

### 扩散模型推理
```bash
python diffusion_inference.py \
    --MS_data_dir ./data/test/MS \
    --SAR_data_dir ./data/test/SAR \
    --output_dir ./results \
    --model_path ./data/diffusion_models/best_model.pth
```

### 传统SRCNN训练
```bash
python train.py \
    --MS_data_dir ./data/train/MS \
    --NTL_data_dir ./data/train/SAR \
    --epochs 10
```

## 依赖环境

- Python 3.7+
- PyTorch 1.8+
- torchvision
- rasterio
- numpy
- matplotlib
- tqdm
- pillow

## 性能对比

| 方法 | 优势 | 劣势 |
|------|------|------|
| SRCNN | 训练快速，结构简单 | 需要标签数据，细节生成有限 |
| 扩散模型 | 无监督，细节丰富，质量高 | 训练时间长，推理较慢 |

## 开发注意事项

1. **数据预处理**：确保SAR和多光谱影像正确配准
2. **内存管理**：扩散模型训练需要较大内存
3. **超参数调优**：根据数据特点调整损失权重
4. **模型选择**：根据应用场景选择合适的方法

## 未来改进方向

1. 模型压缩和加速
2. 实时处理能力
3. 多时相数据融合
4. 自适应参数调整
5. 移动端部署优化
