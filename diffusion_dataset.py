import os
from glob import glob
import torch
from torch.utils.data import Dataset
import rasterio
import numpy as np
import torchvision.transforms.functional as TF
from torchvision import transforms
import random


class DiffusionSARMSDataset(Dataset):
    """
    用于扩散模型的SAR-多光谱数据集
    支持无监督超分辨融合训练
    """
    def __init__(self, MS_data_dir, SAR_data_dir, scale=4, 
                 image_size=256, file_extension='tif',
                 augment=True, normalize=True):
        super().__init__()
        
        self.MS_dir = MS_data_dir
        self.SAR_dir = SAR_data_dir
        self.scale = scale
        self.image_size = image_size
        self.augment = augment
        self.normalize = normalize
        
        # 获取并排序图像文件路径
        self.MS_images = sorted(glob(os.path.join(MS_data_dir, f'*.{file_extension}')))
        self.SAR_images = sorted(glob(os.path.join(SAR_data_dir, f'*.{file_extension}')))
        
        # 确定可用数据对的数量
        self.dataset_size = min(len(self.MS_images), len(self.SAR_images))
        
        if self.dataset_size == 0:
            raise ValueError("一个或多个图像目录为空，请检查路径。")
        
        print(f"找到 {len(self.MS_images)} 个多光谱图像。")
        print(f"找到 {len(self.SAR_images)} 个SAR图像。")
        if len(self.MS_images) != len(self.SAR_images):
            print(f"警告: 图像数量不匹配，将只使用前 {self.dataset_size} 张图像。")
        print(f"可用的匹配数据对数量: {self.dataset_size}")
        
        # 数据增强变换
        if self.augment:
            self.transform = transforms.Compose([
                transforms.RandomHorizontalFlip(p=0.5),
                transforms.RandomVerticalFlip(p=0.5),
                transforms.RandomRotation(degrees=90),
            ])
        else:
            self.transform = None
    
    def __len__(self):
        return self.dataset_size
    
    @staticmethod
    def read_tif_file(file_path):
        """读取TIF文件并返回 (H, W, C) 格式的numpy数组"""
        with rasterio.open(file_path) as src:
            if src.count > 1:
                # 多波段: (C, H, W) -> (H, W, C)
                raw_data = np.transpose(src.read(), (1, 2, 0))
            else:
                # 单波段: (H, W) -> (H, W, 1)
                raw_data = src.read(1)
                raw_data = np.expand_dims(raw_data, axis=-1)
            return raw_data.astype(np.float32)
    
    def normalize_image(self, img_tensor):
        """标准化图像到[0, 1]范围"""
        min_val = img_tensor.min()
        max_val = img_tensor.max()
        if max_val > min_val:
            return (img_tensor - min_val) / (max_val - min_val)
        return img_tensor
    
    def resize_image(self, img_tensor, target_size):
        """调整图像大小"""
        return TF.resize(img_tensor, target_size, interpolation=transforms.InterpolationMode.BILINEAR)
    
    def create_low_res_version(self, img_tensor):
        """创建低分辨率版本（用于超分辨率训练）"""
        # 下采样
        h, w = img_tensor.shape[-2:]
        low_res = TF.resize(img_tensor, (h // self.scale, w // self.scale), 
                           interpolation=transforms.InterpolationMode.BILINEAR)
        # 上采样回原始尺寸
        upsampled = TF.resize(low_res, (h, w), 
                             interpolation=transforms.InterpolationMode.BILINEAR)
        return upsampled, low_res
    
    def apply_augmentation(self, ms_tensor, sar_tensor):
        """应用数据增强"""
        if self.transform is None:
            return ms_tensor, sar_tensor
        
        # 将两个张量合并以确保相同的变换
        combined = torch.cat([ms_tensor, sar_tensor], dim=0)
        combined = self.transform(combined)
        
        # 分离回原始张量
        ms_channels = ms_tensor.shape[0]
        ms_tensor = combined[:ms_channels]
        sar_tensor = combined[ms_channels:]
        
        return ms_tensor, sar_tensor
    
    def create_target_image(self, ms_tensor, sar_tensor):
        """创建目标图像（融合后的高分辨率图像）"""
        # 简单的加权融合作为目标
        # 在实际应用中，这可以是更复杂的融合策略
        
        # 确保SAR是单通道
        if sar_tensor.shape[0] > 1:
            sar_tensor = torch.mean(sar_tensor, dim=0, keepdim=True)
        
        # 将SAR扩展到3通道
        sar_3ch = sar_tensor.repeat(4, 1, 1)
        
        # 取多光谱的前3个通道
        ms_3ch = ms_tensor[:4] if ms_tensor.shape[0] >= 3 else ms_tensor.repeat(3, 1, 1)[:3]
        
        # 加权融合
        alpha = 0.5  # 多光谱权重
        beta = 0.5  # SAR权重
        
        target = alpha * ms_3ch + beta * sar_3ch
        
        return target
    
    def add_noise_for_diffusion(self, img_tensor, noise_level=0.1):
        """为扩散训练添加噪声"""
        noise = torch.randn_like(img_tensor) * noise_level
        return img_tensor + noise
    
    def __getitem__(self, idx):
        """获取一个数据样本"""
        # 读取多光谱图像
        ms_img = self.read_tif_file(self.MS_images[idx])
        ms_tensor = torch.from_numpy(ms_img).permute(2, 0, 1).float()
        
        # 读取SAR图像
        sar_img = self.read_tif_file(self.SAR_images[idx])
        sar_tensor = torch.from_numpy(sar_img).permute(2, 0, 1).float()
        
        # 调整图像大小
        if self.image_size:
            ms_tensor = self.resize_image(ms_tensor, (self.image_size, self.image_size))
            sar_tensor = self.resize_image(sar_tensor, (self.image_size, self.image_size))
        
        # 标准化
        if self.normalize:
            ms_tensor = self.normalize_image(ms_tensor)
            sar_tensor = self.normalize_image(sar_tensor)
        
        # 数据增强
        ms_tensor, sar_tensor = self.apply_augmentation(ms_tensor, sar_tensor)
        
        # 创建目标图像
        target_hr = self.create_target_image(ms_tensor, sar_tensor)
        
        # 创建低分辨率版本（用于超分辨率）
        target_lr, _ = self.create_low_res_version(target_hr)
        
        # 为扩散训练准备数据
        # 这里我们使用目标高分辨率图像作为"干净"图像
        clean_image = target_hr
        
        # 可选：添加轻微噪声来增加训练的鲁棒性
        if self.augment:
            clean_image = self.add_noise_for_diffusion(clean_image, noise_level=0.01)
        
        return {
            'ms': ms_tensor,           # 多光谱图像
            'sar': sar_tensor,         # SAR图像
            'target_hr': target_hr,    # 高分辨率目标
            'target_lr': target_lr,    # 低分辨率目标
            'clean_image': clean_image # 用于扩散训练的干净图像
        }


class DiffusionInferenceDataset(Dataset):
    """
    用于扩散模型推理的数据集
    """
    def __init__(self, MS_data_dir, SAR_data_dir, 
                 image_size=256, file_extension='tif',
                 normalize=True):
        super().__init__()
        
        self.MS_dir = MS_data_dir
        self.SAR_dir = SAR_data_dir
        self.image_size = image_size
        self.normalize = normalize
        
        # 获取并排序图像文件路径
        self.MS_images = sorted(glob(os.path.join(MS_data_dir, f'*.{file_extension}')))
        self.SAR_images = sorted(glob(os.path.join(SAR_data_dir, f'*.{file_extension}')))
        
        # 确定可用数据对的数量
        self.dataset_size = min(len(self.MS_images), len(self.SAR_images))
        
        if self.dataset_size == 0:
            raise ValueError("一个或多个图像目录为空，请检查路径。")
    
    def __len__(self):
        return self.dataset_size
    
    @staticmethod
    def read_tif_file(file_path):
        """读取TIF文件并返回 (H, W, C) 格式的numpy数组"""
        with rasterio.open(file_path) as src:
            if src.count > 1:
                raw_data = np.transpose(src.read(), (1, 2, 0))
            else:
                raw_data = src.read(1)
                raw_data = np.expand_dims(raw_data, axis=-1)
            return raw_data.astype(np.float32)
    
    def normalize_image(self, img_tensor):
        """标准化图像到[0, 1]范围"""
        min_val = img_tensor.min()
        max_val = img_tensor.max()
        if max_val > min_val:
            return (img_tensor - min_val) / (max_val - min_val)
        return img_tensor
    
    def resize_image(self, img_tensor, target_size):
        """调整图像大小"""
        return TF.resize(img_tensor, target_size, interpolation=transforms.InterpolationMode.BILINEAR)
    
    def __getitem__(self, idx):
        """获取一个数据样本"""
        # 读取多光谱图像
        ms_img = self.read_tif_file(self.MS_images[idx])
        ms_tensor = torch.from_numpy(ms_img).permute(2, 0, 1).float()
        
        # 读取SAR图像
        sar_img = self.read_tif_file(self.SAR_images[idx])
        sar_tensor = torch.from_numpy(sar_img).permute(2, 0, 1).float()
        
        # 调整图像大小
        if self.image_size:
            ms_tensor = self.resize_image(ms_tensor, (self.image_size, self.image_size))
            sar_tensor = self.resize_image(sar_tensor, (self.image_size, self.image_size))
        
        # 标准化
        if self.normalize:
            ms_tensor = self.normalize_image(ms_tensor)
            sar_tensor = self.normalize_image(sar_tensor)
        
        return {
            'ms': ms_tensor,
            'sar': sar_tensor,
            'filename': os.path.basename(self.MS_images[idx])
        }


def collate_fn(batch):
    """自定义批处理函数"""
    keys = batch[0].keys()
    result = {}

    for key in keys:
        if key == 'filename':
            result[key] = [item[key] for item in batch]
        else:
            result[key] = torch.stack([item[key] for item in batch])

    return result
