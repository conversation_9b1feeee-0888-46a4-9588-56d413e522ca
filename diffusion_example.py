"""
扩散模型SAR-多光谱融合使用示例
演示如何使用扩散模型进行无监督超分辨融合
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import os

from diffusion_model import DiffusionSRFusion
from diffusion_scheduler import get_scheduler
from diffusion_dataset import DiffusionSARMSDataset, DiffusionInferenceDataset
from diffusion_loss import DiffusionLoss


def create_sample_data():
    """创建示例数据用于演示"""
    # 创建示例多光谱图像 (5通道)
    ms_image = torch.randn(1, 5, 256, 256)
    ms_image = torch.clamp(ms_image * 0.3 + 0.5, 0, 1)
    
    # 创建示例SAR图像 (1通道)
    sar_image = torch.randn(1, 1, 256, 256)
    sar_image = torch.clamp(sar_image * 0.3 + 0.5, 0, 1)
    
    return ms_image, sar_image


def demonstrate_model_architecture():
    """演示模型架构"""
    print("=== 扩散模型架构演示 ===")
    
    # 创建模型
    model = DiffusionSRFusion(
        sar_channels=1,
        ms_channels=5,
        output_channels=3
    )
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建示例输入
    batch_size = 2
    noisy_img = torch.randn(batch_size, 3, 256, 256)
    time = torch.randint(0, 1000, (batch_size,))
    sar = torch.randn(batch_size, 1, 256, 256)
    ms = torch.randn(batch_size, 5, 256, 256)
    
    # 前向传播
    with torch.no_grad():
        output = model(noisy_img, time, sar, ms, apply_sr=True)
    
    print(f"输入形状:")
    print(f"  噪声图像: {noisy_img.shape}")
    print(f"  时间步: {time.shape}")
    print(f"  SAR图像: {sar.shape}")
    print(f"  多光谱图像: {ms.shape}")
    print(f"输出形状: {output.shape}")
    
    return model


def demonstrate_scheduler():
    """演示噪声调度器"""
    print("\n=== 噪声调度器演示 ===")
    
    # 创建不同类型的调度器
    schedulers = {
        'DDPM': get_scheduler('ddpm', num_timesteps=1000),
        'DDIM': get_scheduler('ddim', num_timesteps=1000, num_inference_steps=50),
        'Cosine': get_scheduler('cosine', num_timesteps=1000)
    }
    
    # 创建示例图像
    clean_image = torch.randn(1, 3, 64, 64)
    
    for name, scheduler in schedulers.items():
        print(f"\n{name} 调度器:")
        
        # 采样时间步
        timesteps = scheduler.sample_timesteps(1)
        print(f"  采样时间步: {timesteps.item()}")
        
        # 添加噪声
        noise = torch.randn_like(clean_image)
        noisy_image = scheduler.add_noise(clean_image, noise, timesteps)
        
        print(f"  原始图像范围: [{clean_image.min():.3f}, {clean_image.max():.3f}]")
        print(f"  噪声图像范围: [{noisy_image.min():.3f}, {noisy_image.max():.3f}]")


def demonstrate_loss_functions():
    """演示损失函数"""
    print("\n=== 损失函数演示 ===")
    
    # 创建损失函数
    criterion = DiffusionLoss(
        lambda_mse=1.0,
        lambda_perceptual=0.1,
        lambda_structural=0.1,
        lambda_edge=0.05,
        lambda_spectral=0.05
    )
    
    # 创建示例数据
    pred_noise = torch.randn(2, 3, 128, 128)
    true_noise = torch.randn(2, 3, 128, 128)
    pred_img = torch.rand(2, 3, 128, 128)
    target_img = torch.rand(2, 3, 128, 128)
    
    # 计算损失
    total_loss, loss_dict = criterion(pred_noise, true_noise, pred_img, target_img)
    
    print(f"总损失: {total_loss.item():.4f}")
    print("损失组件:")
    for key, value in loss_dict.items():
        print(f"  {key}: {value:.4f}")


def demonstrate_training_process():
    """演示训练过程"""
    print("\n=== 训练过程演示 ===")
    
    # 创建模型和调度器
    model = DiffusionSRFusion(sar_channels=1, ms_channels=5, output_channels=3)
    scheduler = get_scheduler('ddpm', num_timesteps=100)  # 使用较少时间步用于演示
    criterion = DiffusionLoss()
    
    # 创建示例数据
    ms, sar = create_sample_data()
    
    # 创建目标图像（简单融合）
    target = torch.cat([ms[:, :3], sar.repeat(1, 2, 1, 1)], dim=1).mean(dim=1, keepdim=True).repeat(1, 3, 1, 1)
    
    print("模拟训练步骤:")
    
    for step in range(3):  # 演示3个训练步骤
        # 随机采样时间步
        timesteps = scheduler.sample_timesteps(1)
        
        # 生成噪声
        noise = torch.randn_like(target)
        
        # 添加噪声
        noisy_image = scheduler.add_noise(target, noise, timesteps)
        
        # 前向传播
        predicted_noise = model(noisy_image, timesteps, sar, ms, apply_sr=False)
        
        # 计算损失
        loss, loss_dict = criterion(predicted_noise, noise)
        
        print(f"  步骤 {step + 1}: 损失 = {loss.item():.4f}, 时间步 = {timesteps.item()}")


def demonstrate_inference_process():
    """演示推理过程"""
    print("\n=== 推理过程演示 ===")
    
    # 创建模型和调度器
    model = DiffusionSRFusion(sar_channels=1, ms_channels=5, output_channels=3)
    scheduler = get_scheduler('ddim', num_timesteps=100, num_inference_steps=10)
    
    # 创建条件输入
    ms, sar = create_sample_data()
    
    print("模拟推理过程:")
    
    # 从随机噪声开始
    current_sample = torch.randn(1, 3, 256, 256)
    
    # 获取推理时间步
    if hasattr(scheduler, 'timesteps'):
        timesteps = scheduler.timesteps[:5]  # 只演示前5步
    else:
        timesteps = torch.arange(99, -1, -20)[:5]  # 创建示例时间步
    
    model.eval()
    with torch.no_grad():
        for i, timestep in enumerate(timesteps):
            timestep_tensor = torch.full((1,), timestep, dtype=torch.long)
            
            # 预测噪声
            predicted_noise = model(current_sample, timestep_tensor, sar, ms, apply_sr=False)
            
            # 简单的去噪步骤（演示用）
            current_sample = current_sample - 0.1 * predicted_noise
            
            print(f"  推理步骤 {i + 1}: 时间步 = {timestep}, 图像范围 = [{current_sample.min():.3f}, {current_sample.max():.3f}]")
    
    print(f"最终生成图像形状: {current_sample.shape}")


def visualize_diffusion_process():
    """可视化扩散过程"""
    print("\n=== 扩散过程可视化 ===")
    
    try:
        # 创建调度器
        scheduler = get_scheduler('ddpm', num_timesteps=1000)
        
        # 创建示例图像
        clean_image = torch.rand(1, 3, 64, 64)
        
        # 在不同时间步添加噪声
        timesteps = [0, 100, 300, 500, 700, 999]
        
        fig, axes = plt.subplots(1, len(timesteps), figsize=(15, 3))
        
        for i, t in enumerate(timesteps):
            timestep_tensor = torch.tensor([t])
            noise = torch.randn_like(clean_image)
            noisy_image = scheduler.add_noise(clean_image, noise, timestep_tensor)
            
            # 转换为可视化格式
            img_np = noisy_image.squeeze(0).permute(1, 2, 0).clamp(0, 1).numpy()
            
            axes[i].imshow(img_np)
            axes[i].set_title(f't={t}')
            axes[i].axis('off')
        
        plt.suptitle('扩散过程：从清晰到噪声')
        plt.tight_layout()
        plt.savefig('diffusion_process.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("扩散过程可视化已保存为 'diffusion_process.png'")
        
    except Exception as e:
        print(f"可视化失败: {e}")


def main():
    """主演示函数"""
    print("扩散模型SAR-多光谱融合演示")
    print("=" * 50)
    
    # 设置随机种子以确保可重复性
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 演示各个组件
    demonstrate_model_architecture()
    demonstrate_scheduler()
    demonstrate_loss_functions()
    demonstrate_training_process()
    demonstrate_inference_process()
    visualize_diffusion_process()
    
    print("\n=== 使用建议 ===")
    print("1. 训练命令:")
    print("   python diffusion_train.py --MS_data_dir ./data/train/MS --SAR_data_dir ./data/train/SAR --epochs 100")
    print("\n2. 推理命令:")
    print("   python diffusion_inference.py --MS_data_dir ./data/test/MS --SAR_data_dir ./data/test/SAR --output_dir ./results --model_path ./data/diffusion_models/best_model.pth")
    print("\n3. 关键优势:")
    print("   - 无监督学习，不需要高分辨率标签")
    print("   - 更好的细节生成能力")
    print("   - 灵活的条件控制")
    print("   - 可扩展的架构设计")
    
    print("\n演示完成！")


if __name__ == '__main__':
    main()
