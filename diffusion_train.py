import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau
import os
import numpy as np
import argparse
import logging
from datetime import datetime
from tqdm import tqdm
import matplotlib.pyplot as plt

from diffusion_model import DiffusionSRFusion
from diffusion_scheduler import get_scheduler
from diffusion_loss import DiffusionLoss
from diffusion_dataset import DiffusionSARMSDataset, collate_fn


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('diffusion_training.log'), logging.StreamHandler()]
)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='扩散模型SAR-多光谱融合训练脚本')
    
    # 数据参数
    parser.add_argument('--MS_data_dir', type=str, default='./data/train/MS', help='多光谱图像目录')
    parser.add_argument('--SAR_data_dir', type=str, default='./data/train/SAR', help='SAR图像目录')
    parser.add_argument('--image_size', type=int, default=256, help='训练图像尺寸')
    parser.add_argument('--scale', type=int, default=4, help='超分辨率倍数')
    
    # 训练参数
    parser.add_argument('--batch_size', type=int, default=8, help='训练批次大小')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--lr', type=float, default=1e-4, help='初始学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-5, help='权重衰减参数')
    parser.add_argument('--val_ratio', type=float, default=0.1, help='验证集占总数据的比例')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    # 扩散模型参数
    parser.add_argument('--num_timesteps', type=int, default=1000, help='扩散时间步数')
    parser.add_argument('--scheduler_type', type=str, default='ddpm', 
                       choices=['ddpm', 'ddim', 'cosine'], help='噪声调度器类型')
    parser.add_argument('--beta_start', type=float, default=0.0001, help='噪声调度起始值')
    parser.add_argument('--beta_end', type=float, default=0.02, help='噪声调度结束值')
    
    # 损失函数权重
    parser.add_argument('--lambda_mse', type=float, default=1.0, help='MSE损失权重')
    parser.add_argument('--lambda_perceptual', type=float, default=0.1, help='感知损失权重')
    parser.add_argument('--lambda_structural', type=float, default=0.1, help='结构损失权重')
    parser.add_argument('--lambda_edge', type=float, default=0.05, help='边缘损失权重')
    parser.add_argument('--lambda_spectral', type=float, default=0.05, help='光谱损失权重')
    
    # 其他参数
    parser.add_argument('--save_dir', type=str, default='./data/diffusion_models', help='模型保存目录')
    parser.add_argument('--save_interval', type=int, default=10, help='模型保存间隔(epoch)')
    parser.add_argument('--cuda', action='store_true', default=True, help='使用CUDA')
    parser.add_argument('--num_workers', type=int, default=4, help='数据加载器工作进程数')
    
    return parser.parse_args()


def set_seed(seed):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def train_one_epoch(model, scheduler, dataloader, criterion, optimizer, device, epoch):
    """训练一个epoch"""
    model.train()
    total_loss = 0.0
    loss_components = {}
    
    progress_bar = tqdm(dataloader, desc=f'Epoch {epoch}')
    
    for batch_idx, batch in enumerate(progress_bar):
        # 移动数据到设备
        ms = batch['ms'].to(device)
        sar = batch['sar'].to(device)
        clean_image = batch['clean_image'].to(device)
        
        batch_size = clean_image.shape[0]
        
        # 随机采样时间步
        timesteps = scheduler.sample_timesteps(batch_size)
        
        # 生成随机噪声
        noise = torch.randn_like(clean_image)
        
        # 添加噪声到干净图像
        noisy_image = scheduler.add_noise(clean_image, noise, timesteps)
        
        # 清除梯度
        optimizer.zero_grad()
        
        # 前向传播：预测噪声
        predicted_noise = model(noisy_image, timesteps, sar, ms, apply_sr=False)
        
        # 计算损失
        loss, loss_dict = criterion(predicted_noise, noise, 
                                   pred_img=clean_image, target_img=clean_image)
        
        # 反向传播
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        # 更新参数
        optimizer.step()
        
        # 累计损失
        total_loss += loss.item()
        
        # 累计损失组件
        for key, value in loss_dict.items():
            if key not in loss_components:
                loss_components[key] = 0.0
            loss_components[key] += value
        
        # 更新进度条
        progress_bar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'avg_loss': f'{total_loss / (batch_idx + 1):.4f}'
        })
    
    # 计算平均损失
    avg_loss = total_loss / len(dataloader)
    avg_loss_components = {key: value / len(dataloader) for key, value in loss_components.items()}
    
    return avg_loss, avg_loss_components


def validate(model, scheduler, dataloader, criterion, device):
    """验证模型"""
    model.eval()
    total_loss = 0.0
    loss_components = {}
    
    with torch.no_grad():
        for batch in tqdm(dataloader, desc='Validation'):
            # 移动数据到设备
            ms = batch['ms'].to(device)
            sar = batch['sar'].to(device)
            clean_image = batch['clean_image'].to(device)
            
            batch_size = clean_image.shape[0]
            
            # 随机采样时间步
            timesteps = scheduler.sample_timesteps(batch_size)
            
            # 生成随机噪声
            noise = torch.randn_like(clean_image)
            
            # 添加噪声到干净图像
            noisy_image = scheduler.add_noise(clean_image, noise, timesteps)
            
            # 前向传播：预测噪声
            predicted_noise = model(noisy_image, timesteps, sar, ms, apply_sr=False)
            
            # 计算损失
            loss, loss_dict = criterion(predicted_noise, noise,
                                       pred_img=clean_image, target_img=clean_image)
            
            # 累计损失
            total_loss += loss.item()
            
            # 累计损失组件
            for key, value in loss_dict.items():
                if key not in loss_components:
                    loss_components[key] = 0.0
                loss_components[key] += value
    
    # 计算平均损失
    avg_loss = total_loss / len(dataloader)
    avg_loss_components = {key: value / len(dataloader) for key, value in loss_components.items()}
    
    return avg_loss, avg_loss_components


def save_model(model, optimizer, scheduler, epoch, loss, save_path, config):
    """保存模型"""
    torch.save({
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
        'loss': loss,
        'config': config
    }, save_path)


def plot_training_curves(train_losses, val_losses, save_path):
    """绘制训练曲线"""
    plt.figure(figsize=(12, 4))
    
    # 总损失曲线
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='训练损失')
    plt.plot(val_losses, label='验证损失')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('训练和验证损失')
    plt.grid(True)
    
    # 损失组件曲线（如果有的话）
    plt.subplot(1, 2, 2)
    plt.plot(train_losses, label='总损失')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('损失变化')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()


def main():
    """主训练函数"""
    args = parse_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 记录当前时间
    current_time = datetime.now().strftime('%Y%m%d_%H%M')
    
    # 记录训练配置
    logging.info(f"训练配置: {vars(args)}")
    
    # 确定设备
    device = torch.device('cuda' if torch.cuda.is_available() and args.cuda else 'cpu')
    logging.info(f"使用设备: {device}")
    
    # 创建数据集
    full_dataset = DiffusionSARMSDataset(
        MS_data_dir=args.MS_data_dir,
        SAR_data_dir=args.SAR_data_dir,
        scale=args.scale,
        image_size=args.image_size,
        augment=True,
        normalize=True
    )
    
    # 分割数据集
    dataset_size = len(full_dataset)
    val_size = int(dataset_size * args.val_ratio)
    train_size = dataset_size - val_size
    
    train_dataset, val_dataset = random_split(
        full_dataset,
        [train_size, val_size],
        generator=torch.Generator().manual_seed(args.seed)
    )
    
    logging.info(f"数据集总大小: {dataset_size}")
    logging.info(f"训练集大小: {train_size}")
    logging.info(f"验证集大小: {val_size}")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=args.num_workers,
        pin_memory=True,
        collate_fn=collate_fn
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=args.num_workers,
        pin_memory=True,
        collate_fn=collate_fn
    )
    
    # 创建模型
    model = DiffusionSRFusion(
        sar_channels=1,
        ms_channels=4,
        output_channels=3
    ).to(device)
    
    logging.info(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")
    
    # 创建噪声调度器
    noise_scheduler = get_scheduler(
        scheduler_type=args.scheduler_type,
        num_timesteps=args.num_timesteps,
        beta_start=args.beta_start,
        beta_end=args.beta_end,
        device=device
    )
    
    # 创建损失函数
    criterion = DiffusionLoss(
        lambda_mse=args.lambda_mse,
        lambda_perceptual=args.lambda_perceptual,
        lambda_structural=args.lambda_structural,
        lambda_edge=args.lambda_edge,
        lambda_spectral=args.lambda_spectral
    ).to(device)
    
    # 创建优化器
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    
    # 创建学习率调度器
    lr_scheduler = CosineAnnealingLR(optimizer, T_max=args.epochs, eta_min=1e-7)
    
    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    
    # 开始训练
    for epoch in range(args.epochs):
        # 训练
        train_loss, train_loss_components = train_one_epoch(
            model, noise_scheduler, train_loader, criterion, optimizer, device, epoch + 1
        )
        
        # 验证
        val_loss, val_loss_components = validate(
            model, noise_scheduler, val_loader, criterion, device
        )
        
        # 更新学习率
        lr_scheduler.step()
        
        # 记录损失
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        # 记录日志
        logging.info(f'Epoch [{epoch+1}/{args.epochs}]')
        logging.info(f'训练损失: {train_loss:.4f}, 验证损失: {val_loss:.4f}')
        logging.info(f'学习率: {optimizer.param_groups[0]["lr"]:.8f}')
        
        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_path = os.path.join(args.save_dir, f'best_diffusion_model_{current_time}.pth')
            save_model(model, optimizer, lr_scheduler, epoch, val_loss, best_model_path, vars(args))
            logging.info(f'最佳模型已保存: {best_model_path}')
        
        # 定期保存模型
        if (epoch + 1) % args.save_interval == 0:
            checkpoint_path = os.path.join(args.save_dir, f'diffusion_model_epoch_{epoch+1}_{current_time}.pth')
            save_model(model, optimizer, lr_scheduler, epoch, val_loss, checkpoint_path, vars(args))
            logging.info(f'检查点已保存: {checkpoint_path}')
    
    # 保存最终模型
    final_model_path = os.path.join(args.save_dir, f'final_diffusion_model_{current_time}.pth')
    save_model(model, optimizer, lr_scheduler, args.epochs - 1, val_losses[-1], final_model_path, vars(args))
    logging.info(f'最终模型已保存: {final_model_path}')
    
    # 绘制训练曲线
    curve_path = os.path.join(args.save_dir, f'training_curves_{current_time}.png')
    plot_training_curves(train_losses, val_losses, curve_path)
    logging.info(f'训练曲线已保存: {curve_path}')
    
    logging.info(f"训练完成! 最佳验证损失: {best_val_loss:.4f}")


if __name__ == '__main__':
    main()
