import torch
import torch.nn as nn
import numpy as np


class DDPMScheduler:
    """DDPM噪声调度器"""
    def __init__(self, num_timesteps=1000, beta_start=0.0001, beta_end=0.02, device='cuda'):
        self.num_timesteps = num_timesteps
        self.device = device
        
        # 线性噪声调度
        self.betas = torch.linspace(beta_start, beta_end, num_timesteps, device=device)
        self.alphas = 1.0 - self.betas
        self.alphas_cumprod = torch.cumprod(self.alphas, dim=0)
        self.alphas_cumprod_prev = torch.cat([torch.ones(1, device=device), self.alphas_cumprod[:-1]])
        
        # 计算扩散过程所需的系数
        self.sqrt_alphas_cumprod = torch.sqrt(self.alphas_cumprod)
        self.sqrt_one_minus_alphas_cumprod = torch.sqrt(1.0 - self.alphas_cumprod)
        
        # 反向过程系数
        self.sqrt_recip_alphas = torch.sqrt(1.0 / self.alphas)
        self.sqrt_recipm1_alphas_cumprod = torch.sqrt(1.0 / self.alphas_cumprod - 1)
        
        # 后验方差
        self.posterior_variance = self.betas * (1.0 - self.alphas_cumprod_prev) / (1.0 - self.alphas_cumprod)
    
    def add_noise(self, x_start, noise, timesteps):
        """向原始图像添加噪声（前向过程）"""
        sqrt_alphas_cumprod_t = self.sqrt_alphas_cumprod[timesteps].reshape(-1, 1, 1, 1)
        sqrt_one_minus_alphas_cumprod_t = self.sqrt_one_minus_alphas_cumprod[timesteps].reshape(-1, 1, 1, 1)
        
        return sqrt_alphas_cumprod_t * x_start + sqrt_one_minus_alphas_cumprod_t * noise
    
    def sample_timesteps(self, batch_size):
        """随机采样时间步"""
        return torch.randint(0, self.num_timesteps, (batch_size,), device=self.device)
    
    def get_variance(self, timestep):
        """获取指定时间步的方差"""
        if timestep == 0:
            return 0
        return self.posterior_variance[timestep]
    
    def step(self, model_output, timestep, sample):
        """执行一步反向去噪"""
        # 获取当前时间步的系数
        alpha_t = self.alphas[timestep]
        alpha_cumprod_t = self.alphas_cumprod[timestep]
        beta_t = self.betas[timestep]
        sqrt_one_minus_alpha_cumprod_t = self.sqrt_one_minus_alphas_cumprod[timestep]
        sqrt_recip_alphas_t = self.sqrt_recip_alphas[timestep]
        
        # 预测原始图像
        pred_original_sample = sqrt_recip_alphas_t * (
            sample - beta_t / sqrt_one_minus_alpha_cumprod_t * model_output
        )
        
        # 计算前一时间步的均值
        if timestep == 0:
            prev_sample = pred_original_sample
        else:
            # 获取前一时间步的系数
            alpha_cumprod_prev_t = self.alphas_cumprod_prev[timestep]
            
            # 计算方向向量
            pred_sample_direction = torch.sqrt(1 - alpha_cumprod_prev_t) * model_output
            
            # 计算前一时间步的样本
            prev_sample = torch.sqrt(alpha_cumprod_prev_t) * pred_original_sample + pred_sample_direction
            
            # 添加噪声（如果不是最后一步）
            if timestep > 0:
                variance = self.get_variance(timestep)
                noise = torch.randn_like(sample)
                prev_sample = prev_sample + torch.sqrt(variance) * noise
        
        return prev_sample, pred_original_sample


class DDIMScheduler:
    """DDIM噪声调度器（更快的采样）"""
    def __init__(self, num_timesteps=1000, num_inference_steps=50, beta_start=0.0001, beta_end=0.02, device='cuda'):
        self.num_timesteps = num_timesteps
        self.num_inference_steps = num_inference_steps
        self.device = device
        
        # 线性噪声调度
        self.betas = torch.linspace(beta_start, beta_end, num_timesteps, device=device)
        self.alphas = 1.0 - self.betas
        self.alphas_cumprod = torch.cumprod(self.alphas, dim=0)
        
        # 计算推理时间步
        step_ratio = num_timesteps // num_inference_steps
        self.timesteps = torch.arange(0, num_timesteps, step_ratio, device=device).flip(0)
        
        # 扩散过程系数
        self.sqrt_alphas_cumprod = torch.sqrt(self.alphas_cumprod)
        self.sqrt_one_minus_alphas_cumprod = torch.sqrt(1.0 - self.alphas_cumprod)
    
    def add_noise(self, x_start, noise, timesteps):
        """向原始图像添加噪声"""
        sqrt_alphas_cumprod_t = self.sqrt_alphas_cumprod[timesteps].reshape(-1, 1, 1, 1)
        sqrt_one_minus_alphas_cumprod_t = self.sqrt_one_minus_alphas_cumprod[timesteps].reshape(-1, 1, 1, 1)
        
        return sqrt_alphas_cumprod_t * x_start + sqrt_one_minus_alphas_cumprod_t * noise
    
    def sample_timesteps(self, batch_size):
        """随机采样时间步"""
        return torch.randint(0, self.num_timesteps, (batch_size,), device=self.device)
    
    def step(self, model_output, timestep, sample, eta=0.0):
        """执行一步DDIM去噪"""
        # 找到当前和前一个时间步的索引
        prev_timestep = timestep - self.num_timesteps // self.num_inference_steps
        
        # 获取alpha值
        alpha_cumprod_t = self.alphas_cumprod[timestep]
        alpha_cumprod_t_prev = self.alphas_cumprod[prev_timestep] if prev_timestep >= 0 else torch.tensor(1.0, device=self.device)
        
        beta_cumprod_t = 1 - alpha_cumprod_t
        beta_cumprod_t_prev = 1 - alpha_cumprod_t_prev
        
        # 预测原始样本
        pred_original_sample = (sample - torch.sqrt(beta_cumprod_t) * model_output) / torch.sqrt(alpha_cumprod_t)
        
        # 计算方向向量
        pred_sample_direction = torch.sqrt(beta_cumprod_t_prev) * model_output
        
        # 计算前一时间步的样本
        prev_sample = torch.sqrt(alpha_cumprod_t_prev) * pred_original_sample + pred_sample_direction
        
        return prev_sample, pred_original_sample


class CosineScheduler:
    """余弦噪声调度器"""
    def __init__(self, num_timesteps=1000, s=0.008, device='cuda'):
        self.num_timesteps = num_timesteps
        self.device = device
        
        # 余弦调度
        steps = torch.arange(num_timesteps + 1, device=device, dtype=torch.float32) / num_timesteps
        alphas_cumprod = torch.cos((steps + s) / (1 + s) * torch.pi * 0.5) ** 2
        alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
        
        self.alphas_cumprod = alphas_cumprod
        self.alphas_cumprod_prev = torch.cat([torch.ones(1, device=device), alphas_cumprod[:-1]])
        
        # 计算betas
        self.betas = 1 - (alphas_cumprod / self.alphas_cumprod_prev)
        self.betas = torch.clamp(self.betas, 0, 0.999)
        
        self.alphas = 1.0 - self.betas
        
        # 扩散过程系数
        self.sqrt_alphas_cumprod = torch.sqrt(self.alphas_cumprod)
        self.sqrt_one_minus_alphas_cumprod = torch.sqrt(1.0 - self.alphas_cumprod)
        self.sqrt_recip_alphas = torch.sqrt(1.0 / self.alphas)
        
        # 后验方差
        self.posterior_variance = self.betas * (1.0 - self.alphas_cumprod_prev) / (1.0 - self.alphas_cumprod)
    
    def add_noise(self, x_start, noise, timesteps):
        """向原始图像添加噪声"""
        sqrt_alphas_cumprod_t = self.sqrt_alphas_cumprod[timesteps].reshape(-1, 1, 1, 1)
        sqrt_one_minus_alphas_cumprod_t = self.sqrt_one_minus_alphas_cumprod[timesteps].reshape(-1, 1, 1, 1)
        
        return sqrt_alphas_cumprod_t * x_start + sqrt_one_minus_alphas_cumprod_t * noise
    
    def sample_timesteps(self, batch_size):
        """随机采样时间步"""
        return torch.randint(0, self.num_timesteps, (batch_size,), device=self.device)
    
    def step(self, model_output, timestep, sample):
        """执行一步反向去噪"""
        alpha_t = self.alphas[timestep]
        beta_t = self.betas[timestep]
        sqrt_one_minus_alpha_cumprod_t = self.sqrt_one_minus_alphas_cumprod[timestep]
        sqrt_recip_alphas_t = self.sqrt_recip_alphas[timestep]
        
        # 预测原始图像
        pred_original_sample = sqrt_recip_alphas_t * (
            sample - beta_t / sqrt_one_minus_alpha_cumprod_t * model_output
        )
        
        # 计算前一时间步的均值
        if timestep == 0:
            prev_sample = pred_original_sample
        else:
            variance = self.posterior_variance[timestep]
            noise = torch.randn_like(sample)
            prev_sample = pred_original_sample + torch.sqrt(variance) * noise
        
        return prev_sample, pred_original_sample


def get_scheduler(scheduler_type='ddpm', **kwargs):
    """获取指定类型的调度器"""
    if scheduler_type == 'ddpm':
        return DDPMScheduler(**kwargs)
    elif scheduler_type == 'ddim':
        return DDIMScheduler(**kwargs)
    elif scheduler_type == 'cosine':
        return CosineScheduler(**kwargs)
    else:
        raise ValueError(f"未知的调度器类型: {scheduler_type}")
