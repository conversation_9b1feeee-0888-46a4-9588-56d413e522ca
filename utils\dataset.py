import os
from glob import glob
import torch
from torch.utils.data import Dataset
import rasterio
import numpy as np
import torchvision.transforms.functional as TF

class MedicalImageDataset(Dataset):
    """
    用于加载多光谱（MS）和SAR/夜光（NTL）影像对的数据集类。
    """
    def __init__(self, MS_data_dir, NTL_data_dir, scale, file_extension='tif'):
        super().__init__()
        self.MS_dir = MS_data_dir
        self.scale = scale
        self.NTL_dir = NTL_data_dir

        # 获取并排序图像文件路径
        self.MS_images = sorted(glob(os.path.join(MS_data_dir, f'*.{file_extension}')))
        self.NTL_images = sorted(glob(os.path.join(NTL_data_dir, f'*.{file_extension}')))
        
        # 确定可用数据对的数量
        self.dataset_size = min(len(self.MS_images), len(self.NTL_images))

        if self.dataset_size == 0:
            raise ValueError("一个或多个图像目录为空，请检查路径。")

        print(f"找到 {len(self.MS_images)} 个多光谱图像。")
        print(f"找到 {len(self.NTL_images)} 个SAR/夜光图像。")
        if len(self.MS_images) != len(self.NTL_images):
            print(f"警告: 图像数量不匹配，将只使用前 {self.dataset_size} 张图像。")
        print(f"可用的匹配数据对数量: {self.dataset_size}")

    def __len__(self):
        return self.dataset_size

    @staticmethod
    def read_tif_file(file_path):
        """读取TIF文件并返回 (H, W, C) 格式的numpy数组。"""
        with rasterio.open(file_path) as src:
            if src.count > 1:
                # 多波段: (C, H, W) -> (H, W, C)
                raw_data = np.transpose(src.read(), (1, 2, 0))
            else:
                # 单波段: (H, W) -> (H, W, 1)
                raw_data = src.read(1)
                raw_data = np.expand_dims(raw_data, axis=-1)
            return raw_data

    def get_high_frequency(self, tensor: torch.Tensor, kernel_size: int = 5, sigma: float = 1.0) -> torch.Tensor:
        """通过减去高斯模糊（低频）分量来提取张量的高频细节。"""
        tensor = tensor.float()
        low_freq = TF.gaussian_blur(tensor, kernel_size=kernel_size, sigma=sigma)
        high_freq = tensor - low_freq
        return high_freq

    def __getitem__(self, idx):
        """获取一个数据样本。"""
        # 读取多光谱图像 (MS)
        ms_img = self.read_tif_file(self.MS_images[idx])
        ms_tensor = torch.from_numpy(ms_img).permute(2, 0, 1).float()

        # 读取SAR/夜光图像 (NTL)
        sar_img = self.read_tif_file(self.NTL_images[idx])
        sar_tensor = torch.from_numpy(sar_img).permute(2, 0, 1).float()

        # 1. 分别提取高频细节
        ms_high_freq = self.get_high_frequency(ms_tensor)
        sar_high_freq = self.get_high_frequency(sar_tensor)

        # 2. 将两者的高频细节在通道维度上组合
        combined_high_freq = torch.cat((ms_high_freq, sar_high_freq), dim=0)
        
        # 3. 创建一个3通道的目标张量（例如，用于VGG损失）
        targe = ms_tensor[0:3, :, :]

        # Return the newly created combined high-frequency tensor
        return combined_high_freq, targe, targe