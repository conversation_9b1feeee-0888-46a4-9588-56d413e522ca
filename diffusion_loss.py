import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from torchvision.transforms import Normalize


class PerceptualLoss(nn.Module):
    """感知损失，基于VGG特征"""
    def __init__(self, layers=['relu1_2', 'relu2_2', 'relu3_3', 'relu4_3'], weights=[1.0, 1.0, 1.0, 1.0]):
        super().__init__()
        
        # 加载预训练的VGG16
        try:
            vgg = models.vgg16(weights=models.VGG16_Weights.IMAGENET1K_V1).features
        except:
            # 兼容旧版本PyTorch
            vgg = models.vgg16(pretrained=True).features
        self.layers = layers
        self.weights = weights
        
        # 构建特征提取器
        self.feature_extractor = nn.ModuleDict()
        layer_names = {
            '3': 'relu1_2', '8': 'relu2_2', '15': 'relu3_3', '22': 'relu4_3'
        }
        
        current_layer = nn.Sequential()
        for i, layer in enumerate(vgg):
            current_layer.add_module(str(i), layer)
            if str(i) in layer_names:
                self.feature_extractor[layer_names[str(i)]] = current_layer
                current_layer = nn.Sequential()
        
        # 冻结参数
        for param in self.feature_extractor.parameters():
            param.requires_grad = False
        
        # 图像标准化
        self.normalize = Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    
    def forward(self, pred, target):
        try:
            # 确保输入是3通道
            if pred.size(1) != 3:
                pred = pred.repeat(1, 3, 1, 1) if pred.size(1) == 1 else pred[:, :3]
            if target.size(1) != 3:
                target = target.repeat(1, 3, 1, 1) if target.size(1) == 1 else target[:, :3]

            # 确保值在[0, 1]范围内
            pred = torch.clamp(pred, 0, 255)
            target = torch.clamp(target, 0, 255)

            # 标准化
            pred = self.normalize(pred)
            target = self.normalize(target)

            loss = 0.0
            for layer_name, weight in zip(self.layers, self.weights):
                if layer_name in self.feature_extractor:
                    pred_feat = self.feature_extractor[layer_name](pred)
                    target_feat = self.feature_extractor[layer_name](target)
                    loss += weight * F.mse_loss(pred_feat, target_feat)

            return loss
        except Exception as e:
            # 如果感知损失计算失败，返回零损失
            print(f"感知损失计算失败: {e}")
            return torch.tensor(0.0, device=pred.device, requires_grad=True)


class StructuralLoss(nn.Module):
    """结构损失，保持SAR的结构信息"""
    def __init__(self, window_size=11, sigma=1.5):
        super().__init__()
        self.window_size = window_size
        self.sigma = sigma
        
        # 创建高斯窗口
        self.register_buffer('window', self._create_window(window_size, sigma))
    
    def _create_window(self, window_size, sigma):
        """创建高斯窗口"""
        coords = torch.arange(window_size, dtype=torch.float32)
        coords -= window_size // 2
        
        g = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
        g /= g.sum()
        
        window = g.outer(g).unsqueeze(0).unsqueeze(0)
        return window
    
    def _ssim(self, img1, img2):
        """计算SSIM"""
        C1 = 0.01 ** 2
        C2 = 0.03 ** 2
        
        mu1 = F.conv2d(img1, self.window, padding=self.window_size//2, groups=img1.size(1))
        mu2 = F.conv2d(img2, self.window, padding=self.window_size//2, groups=img2.size(1))
        
        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1 * mu2
        
        sigma1_sq = F.conv2d(img1 * img1, self.window, padding=self.window_size//2, groups=img1.size(1)) - mu1_sq
        sigma2_sq = F.conv2d(img2 * img2, self.window, padding=self.window_size//2, groups=img2.size(1)) - mu2_sq
        sigma12 = F.conv2d(img1 * img2, self.window, padding=self.window_size//2, groups=img1.size(1)) - mu1_mu2
        
        ssim_map = ((2 * mu1_mu2 + C1) * (2 * sigma12 + C2)) / ((mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2))
        
        return ssim_map.mean()
    
    def forward(self, pred, target):
        # 转换为灰度图像计算结构相似性
        if pred.size(1) > 1:
            pred_gray = torch.mean(pred, dim=1, keepdim=True)
        else:
            pred_gray = pred
            
        if target.size(1) > 1:
            target_gray = torch.mean(target, dim=1, keepdim=True)
        else:
            target_gray = target
        
        # 扩展窗口到匹配通道数
        if self.window.size(1) != pred_gray.size(1):
            window = self.window.expand(pred_gray.size(1), -1, -1, -1)
            self.register_buffer('window', window)
        
        ssim = self._ssim(pred_gray, target_gray)
        return 1 - ssim


class EdgeLoss(nn.Module):
    """边缘损失，保持边缘信息"""
    def __init__(self):
        super().__init__()
        
        # Sobel算子
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32)
        
        self.register_buffer('sobel_x', sobel_x.view(1, 1, 3, 3))
        self.register_buffer('sobel_y', sobel_y.view(1, 1, 3, 3))
    
    def _get_edges(self, img):
        """提取边缘"""
        if img.size(1) > 1:
            img = torch.mean(img, dim=1, keepdim=True)
        
        edge_x = F.conv2d(img, self.sobel_x, padding=1)
        edge_y = F.conv2d(img, self.sobel_y, padding=1)
        
        edges = torch.sqrt(edge_x ** 2 + edge_y ** 2)
        return edges
    
    def forward(self, pred, target):
        pred_edges = self._get_edges(pred)
        target_edges = self._get_edges(target)
        
        return F.mse_loss(pred_edges, target_edges)


class SpectralLoss(nn.Module):
    """光谱损失，保持光谱信息"""
    def __init__(self):
        super().__init__()
    
    def forward(self, pred, target):
        # 计算每个通道的均值和标准差
        pred_mean = torch.mean(pred, dim=[2, 3], keepdim=True)
        target_mean = torch.mean(target, dim=[2, 3], keepdim=True)
        
        pred_std = torch.std(pred, dim=[2, 3], keepdim=True)
        target_std = torch.std(target, dim=[2, 3], keepdim=True)
        
        # 光谱损失
        mean_loss = F.mse_loss(pred_mean, target_mean)
        std_loss = F.mse_loss(pred_std, target_std)
        
        return mean_loss + std_loss


class DiffusionLoss(nn.Module):
    """扩散模型综合损失函数"""
    def __init__(self, 
                 lambda_mse=1.0,
                 lambda_perceptual=0.1,
                 lambda_structural=0.1,
                 lambda_edge=0.05,
                 lambda_spectral=0.05):
        super().__init__()
        
        self.lambda_mse = lambda_mse
        self.lambda_perceptual = lambda_perceptual
        self.lambda_structural = lambda_structural
        self.lambda_edge = lambda_edge
        self.lambda_spectral = lambda_spectral
        
        # 损失函数组件
        self.mse_loss = nn.MSELoss()
        self.perceptual_loss = PerceptualLoss()
        self.structural_loss = StructuralLoss()
        self.edge_loss = EdgeLoss()
        self.spectral_loss = SpectralLoss()
    
    def forward(self, pred_noise, true_noise, pred_img=None, target_img=None):
        """
        计算扩散模型损失
        
        Args:
            pred_noise: 模型预测的噪声
            true_noise: 真实噪声
            pred_img: 预测的图像（可选，用于额外损失）
            target_img: 目标图像（可选，用于额外损失）
        """
        # 主要的噪声预测损失
        noise_loss = self.mse_loss(pred_noise, true_noise)
        total_loss = self.lambda_mse * noise_loss
        
        loss_dict = {'noise_loss': noise_loss.item()}
        
        # 如果提供了图像，计算额外的损失
        if pred_img is not None and target_img is not None:
            # 感知损失
            if self.lambda_perceptual > 0:
                perc_loss = self.perceptual_loss(pred_img, target_img)
                total_loss += self.lambda_perceptual * perc_loss
                loss_dict['perceptual_loss'] = perc_loss.item()
            
            # 结构损失
            if self.lambda_structural > 0:
                struct_loss = self.structural_loss(pred_img, target_img)
                total_loss += self.lambda_structural * struct_loss
                loss_dict['structural_loss'] = struct_loss.item()
            
            # 边缘损失
            if self.lambda_edge > 0:
                edge_loss = self.edge_loss(pred_img, target_img)
                total_loss += self.lambda_edge * edge_loss
                loss_dict['edge_loss'] = edge_loss.item()
            
            # 光谱损失
            if self.lambda_spectral > 0:
                spectral_loss = self.spectral_loss(pred_img, target_img)
                total_loss += self.lambda_spectral * spectral_loss
                loss_dict['spectral_loss'] = spectral_loss.item()
        
        loss_dict['total_loss'] = total_loss.item()
        
        return total_loss, loss_dict


class ConsistencyLoss(nn.Module):
    """一致性损失，用于无监督训练"""
    def __init__(self):
        super().__init__()
        self.mse_loss = nn.MSELoss()
    
    def forward(self, pred1, pred2):
        """计算两个预测之间的一致性损失"""
        return self.mse_loss(pred1, pred2)


class CycleLoss(nn.Module):
    """循环一致性损失"""
    def __init__(self):
        super().__init__()
        self.l1_loss = nn.L1Loss()
    
    def forward(self, reconstructed, original):
        """计算重建图像与原始图像的循环损失"""
        return self.l1_loss(reconstructed, original)
